# Willow Project Overview

Willow is a healthcare web app for GLP-1 medications (like Semaglutide) with patient onboarding, doctor verification,
and prescription management. The platform follows domain-driven design with XState for state management.

## Architecture

- **Monorepo**: Turborepo with 3 Next.js frontends + Nest.js backend
- **Database**: Postgres with Prisma
- **UI**: Tai<PERSON><PERSON>, Shadcn, Lucide icons
- **State**: React Query v5, XState v5, Jo<PERSON>i
- **Integrations**: Stripe (payments), DoseSpot (pharmacy), Segment (analytics)
- **Infrastructure**: Docker Compose (local), AWS (ECS Fargate for backend, Amplify for frontends)

## Key Commands

```bash
# Setup
pnpm i                                  # Install dependencies
pnpm add -F {app} {dependency}          # Add dependencies

# Development
pnpm -F api dev                         # Start API server
pnpm dev:api                            # Start API with hot reloading for shared packages
pnpm -F patients dev                    # Patient frontend (port 3000)
pnpm -F doctors dev                     # Doctor frontend (port 3001)
pnpm -F admin dev                       # Admin frontend (port 3002)

# Database
docker compose up -d                    # Start database and Redis
pnpm -F db migrate --name {migration}   # DONT RUN MIGRATIONS! pause and output the cli line to run the migration myself
pnpm -F restore-database start --name=clean_slate  # Initialize database from clean slate

# Output from the API
command tmux capture-pane -t willow-glp90:api -p -S -{lines}

# Testing
pnpm -F onboard start --name ramiro --number 1 --state AZ  # Test patient onboarding

# Code Quality
pnpm format:fix                         # Fix formatting
pnpm lint                               # Lint code
pnpm typecheck                          # Check TypeScript types
```

## Code Conventions

- TypeScript with strict typing
- Import types with `import type { Type } from 'module'`
- Use interfaces for type definitions
- No unused variables (prefix with _ if needed)
- No direct process.env access (use environment modules)
- No non-null assertions (!)
- Explicit error handling with try/catch
- PascalCase for components, camelCase for utilities
- Organized imports (external first, then internal)

## Testing

### E2E Manual Testing

- use Puppeteer MCP
- have all 3 FE apps and api running
- `pnpm -F onboard start --name ramiro --number 1` to onboard a patient
- `pnpm -F backup-database start --env local --name ${name}` to backup the database for manual testing
- `pnpm -F restore-database start --name ${name}` to restore the database for manual testing

#### credentials

Make a POST request to the following endpoints with the following bodies, and store the accessToken in the JSON response:

```
https://localhost:API_PORT/admin/signIn
{ email: "<EMAIL>", password: "somePassword1234$" }

https://localhost:API_PORT/doctor/signIn
{ email: "<EMAIL>", password: "somePassword1234$" }

https://localhost:API_PORT/patient/signIn
{ email: "<EMAIL>", password: "somePassword1234$" }
```

#### onboard test patient

`pnpm -F onboard start --name ramiro --number 1`

## Shared Packages

The monorepo contains several shared packages with different configurations based on their usage:

### Package Types

1. **Built packages (dual format)** - `@willow/auth`: Uses tsup to build both ESM and CommonJS for cross-environment compatibility
2. **Source-only packages** - `@willow/utils`, `@willow/db`: Export TypeScript source directly, transpiled by consuming apps
3. **UI packages** - `@willow/ui`: Frontend-only components using transpilePackages

### Creating Built Packages (Frontend + Backend)

For packages used by both Next.js frontends and NestJS backend (like `@willow/auth`):
#### 1. Package Configuration
```json
{
  "name": "@willow/package-name",
  "version": "0.1.0",
  "private": true,
  "license": "MIT",
  "main": "./dist/index.js",
  "module": "./dist/index.mjs",
  "types": "./dist/index.d.ts",
  "files": ["dist/**", "src/**"],
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "import": "./dist/index.mjs",
      "require": "./dist/index.js"
    }
  },
  "scripts": {
    "build": "tsup",
    "prepare": "pnpm build",
    "dev": "tsup --watch"
  }
}
```

#### 2. Build Configuration (tsup.config.ts)
```typescript
import { defineConfig } from 'tsup';

export default defineConfig({
  entry: { index: 'src/index.ts' },
  format: ['cjs', 'esm'],
  outExtension({ format }) {
    return { js: format === 'cjs' ? '.js' : '.mjs' };
  },
  dts: true,
  sourcemap: true,
  clean: true,
  external: ['@willow/db'], // External dependencies
  platform: 'neutral'
});
```

#### 3. Frontend Integration
Add to Next.js `transpilePackages` for hot reloading:
```javascript
// next.config.mjs
{
  transpilePackages: ['@willow/package-name']
}
```

#### 4. Backend Integration (Development)
For source-level debugging in development, update API's tsconfig.json:
```json
{
  "paths": {
    "@willow/package-name": ["../../packages/package-name/src/index.ts"]
  }
}
```

### Creating Source-Only Packages (Frontend-Only or Direct Export)

For packages like `@willow/utils` that export TypeScript source directly:

#### 1. Package Configuration
```json
{
  "name": "@willow/package-name",
  "type": "module",
  "exports": {
    "./*": "./src/*.ts"
  },
  "scripts": {
    "dev": "tsc",
    "typecheck": "tsc --noEmit"
  }
}
```

#### 2. Frontend Integration
Add to Next.js `transpilePackages`:
```javascript
// next.config.mjs
{
  transpilePackages: ['@willow/package-name']
}
```

### Development Workflow

#### For Built Packages with Backend Usage:
1. **Start package in watch mode**: `pnpm -F package-name dev`
2. **Start API**: `pnpm -F api dev` (will use source files for debugging)
3. **Start frontend**: `pnpm -F app-name dev` (will use transpilePackages)

#### For Source-Only Packages:
1. **Start frontend**: `pnpm -F app-name dev` (transpiles automatically)
2. No build step needed - changes are live

### Key Principles

1. **Frontends**: Use `transpilePackages` for all shared packages
2. **Backend development**: Use TypeScript path mapping to source files for debugging
3. **Backend production**: Uses built CommonJS files from dist/
4. **Build packages**: Only when backend compatibility is required
5. **Source packages**: For frontend-only or when build complexity isn't needed

This setup ensures packages can be used seamlessly in both the NestJS API (CommonJS) and the Next.js frontends (ESM) without maintaining separate code for each environment.

## Github

- Org: Willow-Health-Services
- Repo: willow-monorepo

## Datadog

- site: us5.datadoghq.com

## ClickUp

- generate branch names like: `{custom-id}-{ticket-title-in-kebab-case}`
- add testing instructions to the bottom of the description, like:
```
### Testing

1. step 1
2. step 2
...
```

## Commit Format

Short, descriptive commits with scope:

- `feat(api): add endpoint to cancel patient`
- `fix(patients): repair cancel button functionality`
- `chore(doctors): resolve lint issues`
- `refactor(tools): improve utility functions`

## PR Template

Title: `{ticket-id} - {meaningful-title}`, for example: `PROD-1104 - Cancel patient button`

```markdown
# Context 📝

https://app.clickup.com/t/**********/{ticket-id}

# What Changed 🔧

- {Short bullet list of changes}

# Verification ✅

1. {Steps to verify changes}
```
Don't add any indication of Claude in the PR

# Using Gemini CLI for Large Codebase Analysis

When analyzing large codebases or multiple files that might exceed context limits, use the Gemini CLI with its massive
context window. Use `gemini -p` to leverage Google Gemini's large context capacity.

## File and Directory Inclusion Syntax

Use the `@` syntax to include files and directories in your Gemini prompts. The paths should be relative to WHERE you run the
gemini command:

### Examples:

**Single file analysis:**
gemini -p "@src/main.py Explain this file's purpose and structure"

Multiple files:
gemini -p "@package.json @src/index.js Analyze the dependencies used in the code"

Entire directory:
gemini -p "@src/ Summarize the architecture of this codebase"

Multiple directories:
gemini -p "@src/ @tests/ Analyze test coverage for the source code"

Current directory and subdirectories:
gemini -p "@./ Give me an overview of this entire project"

#### Or use --all_files flag:
gemini --all_files -p "Analyze the project structure and dependencies"

### Implementation Verification Examples

Check if a feature is implemented:
gemini -p "@src/ @lib/ Has dark mode been implemented in this codebase? Show me the relevant files and functions"

Verify authentication implementation:
gemini -p "@src/ @middleware/ Is JWT authentication implemented? List all auth-related endpoints and middleware"

Check for specific patterns:
gemini -p "@src/ Are there any React hooks that handle WebSocket connections? List them with file paths"

Verify error handling:
gemini -p "@src/ @api/ Is proper error handling implemented for all API endpoints? Show examples of try-catch blocks"

Check for rate limiting:
gemini -p "@backend/ @middleware/ Is rate limiting implemented for the API? Show the implementation details"

Verify caching strategy:
gemini -p "@src/ @lib/ @services/ Is Redis caching implemented? List all cache-related functions and their usage"

Check for specific security measures:
gemini -p "@src/ @api/ Are SQL injection protections implemented? Show how user inputs are sanitized"

Verify test coverage for features:
gemini -p "@src/payment/ @tests/ Is the payment processing module fully tested? List all test cases"

### When to Use Gemini CLI

Use gemini -p when:
- Analyzing entire codebases or large directories
- Comparing multiple large files
- Need to understand project-wide patterns or architecture
- Current context window is insufficient for the task
- Working with files totaling more than 100KB
- Verifying if specific features, patterns, or security measures are implemented
- Checking for the presence of certain coding patterns across the entire codebase

### Important Notes

- Paths in @ syntax are relative to your current working directory when invoking gemini
- The CLI will include file contents directly in the context
- No need for --yolo flag for read-only analysis
- Gemini's context window can handle entire codebases that would overflow Claude's context
- When checking implementations, be specific about what you're looking for to get accurate results
