<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversation Simulation Report - <EMAIL></title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
            margin-top: 30px;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
        }
        .info-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .info-card p {
            margin: 0;
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
        }
        .batch {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .batch-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .batch-number {
            font-size: 1.2em;
            font-weight: bold;
            color: #3498db;
        }
        .batch-info {
            color: #6c757d;
            font-size: 0.9em;
        }
        .message {
            background-color: white;
            border-left: 3px solid #3498db;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 0 4px 4px 0;
        }
        .message.patient {
            border-left-color: #2ecc71;
        }
        .message.doctor {
            border-left-color: #e74c3c;
        }
        .message.prev {
            border-left-color: #f39c12;
            opacity: 0.7;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.85em;
            color: #6c757d;
        }
        .message-user {
            font-weight: bold;
            text-transform: uppercase;
        }
        .message-content {
            color: #333;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: 500;
            margin-right: 5px;
        }
        .badge-doctor {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .badge-ps {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }
        .badge-intercom {
            background-color: #e8f5e9;
            color: #388e3c;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
            margin: 20px 0;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -24px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #3498db;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #dee2e6;
        }
        .timeline-content {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .summary-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }
        .summary-card h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #3498db;
            transition: width 0.3s ease;
            text-align: center;
            color: white;
            font-size: 0.85em;
            line-height: 20px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Conversation Simulation Report</h1>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>Patient</h3>
                <p>Susan Akram</p>
                <small><EMAIL></small>
            </div>
            <div class="info-card">
                <h3>Simulation Date</h3>
                <p>7/9/2025, 7:58:54 PM</p>
            </div>
            <div class="info-card">
                <h3>Models Tested</h3>
                <p>sonnet</p>
            </div>
            <div class="info-card">
                <h3>Mode</h3>
                <p>Non-interactive</p>
            </div>
            <div class="info-card">
                <h3>Total Messages</h3>
                <p>50</p>
            </div>
            <div class="info-card">
                <h3>Routing Batches</h3>
                <p>23</p>
            </div>
        </div>

        <h2>🔄 Routing Statistics</h2>
        <table>
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Count</th>
                    <th>Percentage</th>
                    <th>Visual</th>
                </tr>
            </thead>
            <tbody>
                
                <tr>
                    <td>Total Routing Batches</td>
                    <td>23</td>
                    <td>100%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%">100%</div>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td>Doctor-Relevant</td>
                    <td>17</td>
                    <td>74%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 74%">74%</div>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td>PS-Relevant</td>
                    <td>6</td>
                    <td>26%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 26%">26%</div>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td>Both Doctor & PS</td>
                    <td>0</td>
                    <td>0%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%">0%</div>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td>Neither (No Action)</td>
                    <td>0</td>
                    <td>0%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%">0%</div>
                        </div>
                    </td>
                </tr>
                
            </tbody>
        </table>

        <h2>📝 Batch Analysis</h2>
        
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 1</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>11/29 20:15</span>
            </div>
            <div class="message-content">Hi, your next refill of Tirzepatide will be processing in 2 days. Please use 5mg when you start the new vial.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>12/1 00:43</span>
          </div>
          <div class="message-content">Sounds good. Thank you.</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>7814</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient acknowledged the doctor&#39;s instructions about their next refill and dosage change to 5mg.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">ACKNOWLEDGMENT</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 2</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev patient">
            <div class="message-header">
                <span class="message-user">[PREV MSG - PATIENT]</span>
                <span>12/1 00:43</span>
            </div>
            <div class="message-content">Sounds good. Thank you.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>12/1 22:35</span>
          </div>
          <div class="message-content">Is 5 mg up to the 30 unit mark?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>6020</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is asking for clarification about whether 5mg corresponds to the 30 unit mark on their syringe for their tirzepatide injection.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">DOSAGE_QUESTION</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 3</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>12/2 14:42</span>
            </div>
            <div class="message-content">Yes, that is correct I am sorry I should have included that information.  5 mg is the 30 unit dose.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>12/17 01:29</span>
          </div>
          <div class="message-content">Hi, I take my last dose from this vial on January 2nd. I will be out of the country until January 4th. Can I order my next vial now and ask for it to arrive after January 4th?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="error">❌</span></td>
                  <td><span class="success">✅</span></td>
                  <td>7365</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is requesting to order their next vial of medication now but asking for it to be delivered after January 4th because they will be out of the country until that date. They will take their last dose from the current vial on January 2nd and need the delivery timing coordinated around their travel schedule.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">SHIPPING_INQUIRY</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            
            <span class="badge badge-ps">Sent to Patient Services</span>
            <span class="badge badge-intercom">Send to Intercom</span>
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 4</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>12/17 15:36</span>
            </div>
            <div class="message-content">Sure, I will put the prescription through now and asked the pharmacy to hold it until January 4.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>12/18 11:10</span>
          </div>
          <div class="message-content">Thank you.</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>6250</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is thanking the doctor for arranging to hold their prescription until January 4th when they return from being out of the country.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">ACKNOWLEDGMENT</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 5</span>
            <span class="batch-info">2 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev patient">
            <div class="message-header">
                <span class="message-user">[PREV MSG - PATIENT]</span>
                <span>12/18 11:10</span>
            </div>
            <div class="message-content">Thank you.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>1/7 20:43</span>
          </div>
          <div class="message-content">Just checking that it is being sent</div>
      </div>
      
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">2. PATIENT</span>
              <span>1/7 20:43</span>
          </div>
          <div class="message-content">My next dose is Thursday</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="error">❌</span></td>
                  <td><span class="success">✅</span></td>
                  <td>8005</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is following up to check if their medication is being sent as previously arranged, mentioning that their next dose is scheduled for Thursday. This appears to be a continuation of their earlier shipping coordination request from December where they asked for delayed delivery after returning from travel.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">SHIPPING_INQUIRY</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            
            <span class="badge badge-ps">Sent to Patient Services</span>
            <span class="badge badge-intercom">Send to Intercom</span>
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 6</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>1/8 11:58</span>
            </div>
            <div class="message-content">I will contact patient services and have them ask the pharmacy.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>1/9 08:50</span>
          </div>
          <div class="message-content">Is there an update?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="error">❌</span></td>
                  <td><span class="success">✅</span></td>
                  <td>8636</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is following up asking for an update on their medication delivery status. This is a continuation of their previous shipping inquiry where they were checking if their medication was being sent, with their next dose scheduled for Thursday. The doctor had previously said they would contact patient services to check with the pharmacy about the delivery status.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">SHIPPING_INQUIRY</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            
            <span class="badge badge-ps">Sent to Patient Services</span>
            <span class="badge badge-intercom">Updated previous intercom chat</span>
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 7</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>1/9 10:56</span>
            </div>
            <div class="message-content">No, I have not heard back yet.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>1/10 09:19</span>
          </div>
          <div class="message-content">Is there someone I need to contact because I was supposed to take my weekly dose yesterday</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="error">❌</span></td>
                  <td><span class="success">✅</span></td>
                  <td>8427</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is urgently following up on their delayed medication delivery, stating they were supposed to take their weekly dose yesterday and asking if there is someone they need to contact. This is a continuation of their ongoing shipping inquiry where they have been waiting for their medication that was supposed to be held until January 4th and shipped after they returned from being out of the country. The patient is expressing urgency due to missing their scheduled dose.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">SHIPPING_INQUIRY</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            
            <span class="badge badge-ps">Sent to Patient Services</span>
            <span class="badge badge-intercom">Updated previous intercom chat</span>
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 8</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>1/10 16:33</span>
            </div>
            <div class="message-content">You can message patient care as well through the portal. I am not sure why I have not heard back from them, I am sorry.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>1/11 14:51</span>
          </div>
          <div class="message-content">I wrote to them yesterday. Still no clear idea of what’s happening.</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="error">❌</span></td>
                  <td><span class="success">✅</span></td>
                  <td>8111</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is following up on their delayed medication delivery, stating they contacted patient services yesterday but still have no clear information about what&#39;s happening with their shipment. This continues their ongoing shipping inquiry from previous days where they expressed urgency due to missing their scheduled weekly dose.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">SHIPPING_INQUIRY</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            
            <span class="badge badge-ps">Sent to Patient Services</span>
            <span class="badge badge-intercom">Updated previous intercom chat</span>
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 9</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>1/11 22:34</span>
            </div>
            <div class="message-content">Hi, I just found a shipping notification that looks like the medication shipped and is being held in refrigeration over the weekend by UPS.  I expect it to be delivered on Monday.  Here is the tracking number 1Z6R903W1313426642</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>1/11 23:45</span>
          </div>
          <div class="message-content">Thank you Dr. Green. Would you let me know if the dosage will be the same?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>6619</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is asking Dr. Green to confirm whether the dosage will remain the same for the upcoming medication delivery that was just tracked and is expected to arrive Monday.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">DOSAGE_QUESTION</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 10</span>
            <span class="batch-info">2 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>1/15 09:27</span>
            </div>
            <div class="message-content">Hi, I am sending your tirzepatide 10 mg refill to the pharmacy. This will come from Redrock Pharmacy and will be shipped by FedEx so the packaging will look different. it will be a strait compounded tirzepatide.

The medication has a unique concentration, you will use 50 units per week to get the 10 mg dose.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>1/15 16:26</span>
          </div>
          <div class="message-content">Hi Dr. Green,
Is this different from the dose I received this weekend for the month?</div>
      </div>
      
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">2. PATIENT</span>
              <span>1/15 16:27</span>
          </div>
          <div class="message-content">Also I am feeling very nauseous. Like not wanting to eat from the dose I took when I finally received the medication on Monday night.</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>8357</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is asking for clarification about whether the new 10mg tirzepatide dose (50 units per week) that the doctor is sending is different from the 7.5mg dose they received over the weekend. Additionally, the patient is reporting significant nausea side effects from the dose they took on Monday night, stating they are feeling very nauseous and not wanting to eat.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">DOSAGE_QUESTION</span><span class="badge badge-ps">SIDE_EFFECTS</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 11</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>1/15 23:36</span>
            </div>
            <div class="message-content">Hi, this dose is the next step up.  The anticipation is we will need to increase the dose after you finish this vial.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>1/18 15:20</span>
          </div>
          <div class="message-content">I had really uncomfortable nausea this last dose. Can I reduce the amount for this week?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>6488</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is reporting uncomfortable nausea from their last dose and asking if they can reduce the medication amount for this week due to the side effects.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">SIDE_EFFECTS</span><span class="badge badge-ps">DOSAGE_QUESTION</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 12</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>2/11 20:47</span>
            </div>
            <div class="message-content">Would you like me to decrease the dose of the next refill?</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>2/12 15:25</span>
          </div>
          <div class="message-content">I would like to pause for the next month</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>6940</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is requesting to pause their treatment for the next month in response to the doctor&#39;s question about decreasing the dose of the next refill.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">TREATMENT_APPROVAL</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 13</span>
            <span class="batch-info">2 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>2/12 20:56</span>
            </div>
            <div class="message-content">Your prescription is paused.  Please let me know when you would like your next refill.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>3/10 20:22</span>
          </div>
          <div class="message-content">I would like to start again</div>
      </div>
      
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">2. PATIENT</span>
              <span>3/10 20:26</span>
          </div>
          <div class="message-content">I would prefer to start again with a low dose.</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>7646</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is requesting to restart their tirzepatide treatment after pausing it for a month in February, and specifically wants to begin again with a low dose.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">TREATMENT_APPROVAL</span><span class="badge badge-ps">DOSAGE_QUESTION</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 14</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>3/11 08:42</span>
            </div>
            <div class="message-content">I am happy to restart your medication.  The lowest dose is 2.5 mg we can also do 5 mg or 7.5 mg.  Which do you prefer?</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>3/11 19:21</span>
          </div>
          <div class="message-content">Is 2.5 less expensive</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>8466</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is asking if the 2.5mg dosage option is less expensive than the other dosage options the doctor mentioned (5mg or 7.5mg) when restarting their treatment.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">MEDICAL_QUESTION</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 15</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>3/12 08:15</span>
            </div>
            <div class="message-content">Tirzepatide is $399 for doses up to 7.5 mg, $499 for the 10 and 12.5 mg doses, and $524 for the 15 mg maximum dose.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>3/12 12:43</span>
          </div>
          <div class="message-content">Let’s do 5 mg</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>7842</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is responding to the doctor&#39;s question about dosage preference by choosing the 5mg dose for restarting their treatment.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">TREATMENT_APPROVAL</span><span class="badge badge-ps">DOSAGE_QUESTION</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 16</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>3/13 12:53</span>
            </div>
            <div class="message-content">I am sending in the 5 mg tirzepatide for you.  You will use 50 units/week when you start the medication.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>3/17 20:26</span>
          </div>
          <div class="message-content">I was charged already. When is it arriving?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="error">❌</span></td>
                  <td><span class="success">✅</span></td>
                  <td>6650</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is asking about the delivery status of their medication after being charged, wanting to know when it will arrive.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">SHIPPING_INQUIRY</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            
            <span class="badge badge-ps">Sent to Patient Services</span>
            <span class="badge badge-intercom">Send to Intercom</span>
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 17</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>3/18 14:39</span>
            </div>
            <div class="message-content">The pharmacy takes about 7 working days to process and fill the medication.  It then gets sent by overnight UPS.  You should get a confirmation with tracking information when the medication ships.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>3/22 23:11</span>
          </div>
          <div class="message-content">I injected 15 units on Friday but it doesn’t seem to be working. Can I take more?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>7343</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is reporting that their medication (15 units injected on Friday) doesn&#39;t seem to be working and asking if they can take more.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">EFFICACY_CONCERN</span><span class="badge badge-ps">DOSAGE_QUESTION</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 18</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>3/23 09:27</span>
            </div>
            <div class="message-content">You were supposed to use 50 units not 15.  You can use the additional 35 units now.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>3/23 09:50</span>
          </div>
          <div class="message-content">I took 15 units 2 days ago. I haven’t noticed it working. Can I take more?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>9389</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is following up on their medication efficacy concern, reporting they took 15 units 2 days ago and haven&#39;t noticed it working, asking if they can take more. This is a continuation of their earlier question from the same day where the doctor already clarified they should have used 50 units instead of 15.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">EFFICACY_CONCERN</span><span class="badge badge-ps">DOSAGE_QUESTION</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 19</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>3/23 12:34</span>
            </div>
            <div class="message-content">Please see my previous response.  I am not sure why you only took 15 units, you should be using 50 units.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>3/23 17:34</span>
          </div>
          <div class="message-content">Ok</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>6913</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is acknowledging the doctor&#39;s previous response about using the correct dosage of 50 units instead of 15 units.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">ACKNOWLEDGMENT</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 20</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>4/1 12:55</span>
            </div>
            <div class="message-content">Hi, your next refill of Tirzepatide will be processing in 2 days. Please use 75 units per week when you start the new vial.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>4/1 23:23</span>
          </div>
          <div class="message-content">Ok</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>6539</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is acknowledging the doctor&#39;s instructions about using 75 units per week when starting the new vial.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">ACKNOWLEDGMENT</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 21</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>4/5 09:00</span>
            </div>
            <div class="message-content">I am authorizing your refill for next month’s 6.375 mg tirzepatide. You will use 75 units per week until your next refill. 

Your Red Rock tirzpatide prescription will now be personalized or your unique needs. It will compounded with glycine. Glycine helps maintain muscle mass, which is very important while losing weight. You will also notice that with the addition of the glycine, the dosing ladder is now slightly changed. Red Rock tirzepatide doses are now as follows: 2.125 mg, 4.25 mg, 6.375 mg, 8.5 mg, 11 mg, and 13.5 mg. You have been prescribed a dose according to our dosing algorithm that is the most appropriate for your personal weight loss trajectory.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>4/11 09:54</span>
          </div>
          <div class="message-content">Ok</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>7120</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is acknowledging the doctor&#39;s instructions about their next refill of 6.375 mg tirzepatide with glycine.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">ACKNOWLEDGMENT</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 22</span>
            <span class="batch-info">1 messages | Trigger: Routing delay reached</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>5/17 09:15</span>
            </div>
            <div class="message-content">Thank you for answering your follow-up questions.  Would you like me to decrease the dose for your next refill?</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>5/21 09:17</span>
          </div>
          <div class="message-content">No I would like to keep it the same.</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>7645</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is responding to the doctor&#39;s question about decreasing their dose for the next refill, confirming they want to keep the same dosage.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">TREATMENT_APPROVAL</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 23</span>
            <span class="batch-info">1 messages | Trigger: End of conversation</span>
        </div>
        
        
        <div class="message prev doctor">
            <div class="message-header">
                <span class="message-user">[PREV MSG - DOCTOR]</span>
                <span>5/22 10:08</span>
            </div>
            <div class="message-content">OK, I will keep you at the same dose.</div>
        </div>
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>7/9 11:13</span>
          </div>
          <div class="message-content">Hi Dr Green, I paused my subscription while I was out of the country. It was supposed to resume on July 15th but I am back home now. Would you please send me an order?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>8401</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is requesting to resume their treatment subscription early. They had paused their subscription while out of the country with a scheduled resume date of July 15th, but they have returned home early and are asking the doctor to send them an order to restart their medication now.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">TREATMENT_APPROVAL</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    

        
        <h2>📮 Intercom Conversations</h2>
        <table>
            <thead>
                <tr>
                    <th>Conversation ID</th>
                    <th>Messages</th>
                    <th>Duration</th>
                    <th>Avg Confidence</th>
                </tr>
            </thead>
            <tbody>
                
                <tr>
                    <td><code>sim-intercom-bed32e52</code></td>
                    <td>1</td>
                    <td>< 1m</td>
                    <td>0.10</td>
                </tr>
                
                <tr>
                    <td><code>sim-intercom-45c1a3b8</code></td>
                    <td>5</td>
                    <td>3d 18h 8m</td>
                    <td>0.88</td>
                </tr>
                
                <tr>
                    <td><code>sim-intercom-b80c716b</code></td>
                    <td>1</td>
                    <td>< 1m</td>
                    <td>0.10</td>
                </tr>
                
            </tbody>
        </table>
        

        
        <h2>🏷️ Inquiry Types</h2>
        <table>
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Count</th>
                    <th>Percentage</th>
                </tr>
            </thead>
            <tbody>
                
                <tr>
                    <td>DOSAGE_QUESTION</td>
                    <td>8</td>
                    <td>35%</td>
                </tr>
                
                <tr>
                    <td>SHIPPING_INQUIRY</td>
                    <td>6</td>
                    <td>26%</td>
                </tr>
                
                <tr>
                    <td>ACKNOWLEDGMENT</td>
                    <td>5</td>
                    <td>22%</td>
                </tr>
                
                <tr>
                    <td>TREATMENT_APPROVAL</td>
                    <td>5</td>
                    <td>22%</td>
                </tr>
                
                <tr>
                    <td>SIDE_EFFECTS</td>
                    <td>2</td>
                    <td>9%</td>
                </tr>
                
                <tr>
                    <td>EFFICACY_CONCERN</td>
                    <td>2</td>
                    <td>9%</td>
                </tr>
                
                <tr>
                    <td>MEDICAL_QUESTION</td>
                    <td>1</td>
                    <td>4%</td>
                </tr>
                
            </tbody>
        </table>
        

        <h2>📅 Conversation Timeline</h2>
        <div class="timeline">
            
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🟢 <strong>Conversation started</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">11/10/2024, 8:07:37 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 20d 4h 40m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 1 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">12/1/2024, 12:48:20 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 21h 52m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 2 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">12/1/2024, 10:40:54 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 15d 2h 53m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 3 routed to PS</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">12/17/2024, 1:34:12 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ < 1m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>📮 <strong>New Intercom conversation created</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">12/17/2024, 1:34:12 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 1d 9h 41m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 4 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">12/18/2024, 11:15:18 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 20d 9h 33m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 5 routed to PS</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">1/7/2025, 8:48:44 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ < 1m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>📮 <strong>New Intercom conversation created</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">1/7/2025, 8:48:44 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 1d 12h 7m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 6 routed to PS</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">1/9/2025, 8:55:57 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 1d 28m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 7 routed to PS</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">1/10/2025, 9:24:40 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 1d 5h 32m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 8 routed to PS</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">1/11/2025, 2:56:49 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 8h 53m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 9 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">1/11/2025, 11:50:03 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 3d 16h 42m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 10 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">1/15/2025, 4:32:11 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 2d 22h 53m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 11 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">1/18/2025, 3:25:33 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 25d 4m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 12 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">2/12/2025, 3:30:06 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 26d 5h 1m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 13 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">3/10/2025, 8:31:07 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 22h 55m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 14 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">3/11/2025, 7:26:22 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 17h 21m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 15 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">3/12/2025, 12:48:10 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 5d 7h 43m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 16 routed to PS</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">3/17/2025, 8:31:52 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ < 1m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>📮 <strong>New Intercom conversation created</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">3/17/2025, 8:31:52 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 5d 2h 44m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 17 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">3/22/2025, 11:16:10 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 10h 39m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 18 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">3/23/2025, 9:55:11 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 7h 44m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 19 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">3/23/2025, 5:39:18 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 9d 5h 49m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 20 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">4/1/2025, 11:28:20 PM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 9d 10h 30m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 21 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">4/11/2025, 9:59:00 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 39d 23h 23m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 22 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">5/21/2025, 9:22:51 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 49d 1h 55m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 23 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">7/9/2025, 11:18:29 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ < 1m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔴 <strong>Conversation ended</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">7/9/2025, 11:18:29 AM</div>
              
          </div>
      </div>
      
        </div>

        <h2>💡 Insights & Recommendations</h2>
        <ul>
            <li>Patient sent 26 messages, doctor sent 24 messages</li><li>High routing rate (100%) - conversation required frequent attention</li><li>Most common inquiry type: "DOSAGE_QUESTION" (8 occurrences)</li><li>Long-running conversation spanning 241 days</li>
        </ul>

        <div class="footer">
            Generated by Willow Conversation Simulation Tool • 7/9/2025, 7:58:54 PM
        </div>
    </div>
</body>
</html>