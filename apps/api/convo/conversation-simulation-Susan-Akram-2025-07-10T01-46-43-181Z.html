<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversation Simulation Report - <EMAIL></title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
            margin-top: 30px;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
        }
        .info-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .info-card p {
            margin: 0;
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
        }
        .batch {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .batch-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .batch-number {
            font-size: 1.2em;
            font-weight: bold;
            color: #3498db;
        }
        .batch-info {
            color: #6c757d;
            font-size: 0.9em;
        }
        .message {
            background-color: white;
            border-left: 3px solid #3498db;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 0 4px 4px 0;
        }
        .message.patient {
            border-left-color: #2ecc71;
        }
        .message.doctor {
            border-left-color: #e74c3c;
        }
        .message.prev {
            border-left-color: #f39c12;
            opacity: 0.7;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.85em;
            color: #6c757d;
        }
        .message-user {
            font-weight: bold;
            text-transform: uppercase;
        }
        .message-content {
            color: #333;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: 500;
            margin-right: 5px;
        }
        .badge-doctor {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .badge-ps {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }
        .badge-intercom {
            background-color: #e8f5e9;
            color: #388e3c;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
            margin: 20px 0;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -24px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #3498db;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #dee2e6;
        }
        .timeline-content {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .summary-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }
        .summary-card h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #3498db;
            transition: width 0.3s ease;
            text-align: center;
            color: white;
            font-size: 0.85em;
            line-height: 20px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Conversation Simulation Report</h1>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>Patient</h3>
                <p>Susan Akram</p>
                <small><EMAIL></small>
            </div>
            <div class="info-card">
                <h3>Simulation Date</h3>
                <p>7/9/2025, 8:46:43 PM</p>
            </div>
            <div class="info-card">
                <h3>Models Tested</h3>
                <p>sonnet</p>
            </div>
            <div class="info-card">
                <h3>Mode</h3>
                <p>Non-interactive</p>
            </div>
            <div class="info-card">
                <h3>Total Messages</h3>
                <p>1</p>
            </div>
            <div class="info-card">
                <h3>Routing Batches</h3>
                <p>1</p>
            </div>
        </div>

        <h2>🔄 Routing Statistics</h2>
        <table>
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Count</th>
                    <th>Percentage</th>
                    <th>Visual</th>
                </tr>
            </thead>
            <tbody>
                
                <tr>
                    <td>Total Routing Batches</td>
                    <td>1</td>
                    <td>100%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%">100%</div>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td>Doctor-Relevant</td>
                    <td>1</td>
                    <td>100%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%">100%</div>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td>PS-Relevant</td>
                    <td>0</td>
                    <td>0%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%">0%</div>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td>Both Doctor & PS</td>
                    <td>0</td>
                    <td>0%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%">0%</div>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td>Neither (No Action)</td>
                    <td>0</td>
                    <td>0%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%">0%</div>
                        </div>
                    </td>
                </tr>
                
            </tbody>
        </table>

        <h2>📝 Batch Analysis</h2>
        
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch 1</span>
            <span class="batch-info">1 messages | Trigger: End of conversation</span>
        </div>
        
        
        
      <div class="message patient">
          <div class="message-header">
              <span class="message-user">1. PATIENT</span>
              <span>7/9 11:13</span>
          </div>
          <div class="message-content">Hi Dr Green, I paused my subscription while I was out of the country. It was supposed to resume on July 15th but I am back home now. Would you please send me an order?</div>
      </div>
      
        
        <h3>Model Analysis Results</h3>
        
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            
              <tr>
                  <td>sonnet</td>
                  <td><span class="success">✅</span></td>
                  <td><span class="error">❌</span></td>
                  <td>7437</td>
                  <td style="word-wrap: break-word; max-width: 400px;">The patient is requesting to resume their paused subscription and asking for an order to be sent. They paused their subscription while out of the country and it was supposed to resume on July 15th, but they are back home now and want to continue treatment.</td>
              </tr>
              
        </tbody>
    </table>
    
        
        
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            <span class="badge badge-ps">TREATMENT_APPROVAL</span><span class="badge badge-ps">PRESCRIPTION_RENEWAL</span>
        </div>
        
        
        
        <div style="margin-top: 15px;">
            <span class="badge badge-doctor">Sent to Doctor</span>
            
            
        </div>
        
    </div>
    

        

        
        <h2>🏷️ Inquiry Types</h2>
        <table>
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Count</th>
                    <th>Percentage</th>
                </tr>
            </thead>
            <tbody>
                
                <tr>
                    <td>TREATMENT_APPROVAL</td>
                    <td>1</td>
                    <td>100%</td>
                </tr>
                
                <tr>
                    <td>PRESCRIPTION_RENEWAL</td>
                    <td>1</td>
                    <td>100%</td>
                </tr>
                
            </tbody>
        </table>
        

        <h2>📅 Conversation Timeline</h2>
        <div class="timeline">
            
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🟢 <strong>Conversation started</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">7/9/2025, 11:13:29 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ 5m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔄 <strong>Batch 1 routed to Doctor</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">7/9/2025, 11:18:29 AM</div>
              <div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ < 1m</div>
          </div>
      </div>
      
      <div class="timeline-item">
          <div class="timeline-content">
              <div>🔴 <strong>Conversation ended</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">7/9/2025, 11:18:29 AM</div>
              
          </div>
      </div>
      
        </div>

        <h2>💡 Insights & Recommendations</h2>
        <ul>
            <li>High routing rate (100%) - conversation required frequent attention</li><li>Most common inquiry type: "TREATMENT_APPROVAL" (1 occurrences)</li><li>Quick conversation resolved within a day</li>
        </ul>

        <div class="footer">
            Generated by Willow Conversation Simulation Tool • 7/9/2025, 8:46:43 PM
        </div>
    </div>
</body>
</html>