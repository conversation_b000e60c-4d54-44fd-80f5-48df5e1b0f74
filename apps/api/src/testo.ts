import { createWriteStream } from 'fs';
import { NestFactory } from '@nestjs/core';

import { AppModule } from './app.module';
import { PatientGetHsaReceiptUseCase } from './modules/patient/use-cases/patient-get-hsa-receipt.use-case';
import { PrismaService } from './modules/prisma/prisma.service';
import { TreatmentNotifyEventListener } from './modules/treatment/events/treatment-notify.event';

process.env.IS_CLI = 'true';
process.env.ENABLE_SUBSCRIPTION_WORKERS = 'false';
process.env.ENABLE_SNS_CONSUMER = 'false';
process.env.ENABLE_SQS_CONSUMER = 'false';

/*
async function testoHSA() {
      process.env.POSTGRES_USER = 'outliantwillow';
  process.env.POSTGRES_PASSWORD = 'HealthWwPgwW615!';
  process.env.POSTGRES_PORT = '5432';
  process.env.POSTGRES_HOST =
    'willow-api-staging-rdsinstance9f6b765a-cqbhvi66inwm.citvpurnsqdf.us-west-2.rds.amazonaws.com';
  process.env.POSTGRES_DB = 'willow';
  process.env.DATABASE_URL =
    '********************************************************************************************************************************************/willow';

  process.env.STRIPE_SECRET_KEY =
    'sk_test_51OMDIzK1cV2ssKTgK5aFahjSfLLNCLt4mCNQf7apEIcs3Le7ycD8VQdGGej6IUwJW4cwEXt8PJXeW9N6ztU0lv6o00ZNXamjAI';

   
  const app = await NestFactory.createApplicationContext(AppModule);
  const patientGetLetterOfMedicalNecessityUseCase = app.get(
    PatientGetLetterOfMedicalNecessityUseCase,
  );

  const document = await patientGetLetterOfMedicalNecessityUseCase.execute(
    '68c18330-0031-70c1-c5e1-d424da3c35ef',
  );
  const writeStream = createWriteStream('hsa-receipt.pdf');
  document.getStream().pipe(writeStream);
  writeStream.on('finish', () => {
    process.exit(0);
  });
}
*/

async function testoNextRefill() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const treatmentNotifyEventListener = app.get(TreatmentNotifyEventListener);
  const prisma = app.get(PrismaService);

  const t = await prisma.treatment.findFirstOrThrow({
    where: { id: '6b6f3f13-ba17-455e-aa8a-16d8d95769ed' },
  });
  await treatmentNotifyEventListener.nextRefill({
    _topic: 'treatment-updated',
    metadata: {
      patientId: 'as',
      timestamp: '1',
      topic: 'treatment-updated',
    },
    payload: {
      event: 'cancelled',
      treatment: t,
    },
  });
}

testoNextRefill();
