import { Module } from '@nestjs/common';

import { PrismaModule } from '../prisma/prisma.module';
import { SharedModule } from '../shared/shared.module';
import { StripeModule } from '../stripe/stripe.module';
import { ProductCategoryController } from './product-category.controller';
import { ProductPriceEquivalenceController } from './product-price-equivalence.controller';
import { ProductPriceMappingController } from './product-price-mapping.controller';
import { ProductPriceController } from './product-price.controller';
import { ProductController } from './product.controller';
import { ProductCategoryService } from './services/product-category.service';
import { ProductPriceEquivalenceService } from './services/product-price-equivalence.service';
import { ProductPriceMappingService } from './services/product-price-mapping.service';
import { ProductPriceService } from './services/product-price.service';
import { ProductService } from './services/product.service';

@Module({
  imports: [PrismaModule, SharedModule, StripeModule],
  controllers: [
    ProductController,
    ProductCategoryController,
    ProductPriceController,
    ProductPriceMappingController,
    ProductPriceEquivalenceController,
  ],
  providers: [
    ProductService,
    ProductCategoryService,
    ProductPriceService,
    ProductPriceMappingService,
    ProductPriceEquivalenceService,
  ],
  exports: [
    ProductService,
    ProductCategoryService,
    ProductPriceService,
    ProductPriceMappingService,
    ProductPriceEquivalenceService,
  ],
})
export class ProductModule {}
