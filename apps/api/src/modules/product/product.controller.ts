import { RequireCapabilities } from '@modules/auth/decorators/require-capabilities.decorator';
import { SuperAdminGuard } from '@modules/auth/decorators/super-admin.decorator';
import { CapabilityGuard } from '@modules/auth/guards/capability.guard';
import { roles } from '@modules/auth/types/roles';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FileInterceptor } from '@nestjs/platform-express';

import { Capability } from '@willow/auth';

import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductService } from './services/product.service';

@Controller('product')
@UseGuards(AuthGuard('jwt'), RolesGuard, CapabilityGuard)
@Roles(roles.Admin)
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Get()
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('direction') direction?: 'asc' | 'desc',
    @Query('productCategoryId') productCategoryId?: string,
  ) {
    return this.productService.findAll({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      search,
      sortBy,
      direction,
      productCategoryId,
    });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.productService.findOne(id);
  }

  @Post()
  create(@Body() createProductDto: CreateProductDto) {
    return this.productService.create(createProductDto);
  }

  @Post('upload-image')
  @UseInterceptors(
    FileInterceptor('image', {
      limits: {
        fileSize: 1024 * 1024 * 5, // 5MB limit
      },
      fileFilter: (req, file, callback) => {
        if (!file.mimetype.match(/^image\/(jpeg|png|jpg|gif)$/)) {
          return callback(
            new BadRequestException('Only image files are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async uploadImage(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }
    return this.productService.uploadImage(file);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto) {
    return this.productService.update(id, updateProductDto);
  }

  @Delete(':id')
  delete(@Param('id') id: string) {
    return this.productService.delete(id);
  }

  @Patch(':id/activate')
  activate(@Param('id') id: string) {
    return this.productService.update(id, { active: true });
  }

  @Patch(':id/deactivate')
  deactivate(@Param('id') id: string) {
    return this.productService.update(id, { active: false });
  }

  @Post('sync')
  async syncProductsFromStripe(@Query('newOnly') newOnly?: boolean) {
    const summary = await this.productService.syncProductsFromStripe(
      newOnly || false,
    );
    return {
      success: true,
      message: 'Products synchronized successfully',
      summary,
    };
  }
}
