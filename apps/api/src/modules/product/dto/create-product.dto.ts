import { ProductForms, ProductTypes } from '@prisma/client';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateProductDto {
  @IsString()
  @IsNotEmpty()
  pharmacyId: string;

  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  productCategoryIds: string[];

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsOptional()
  image?: string;

  @IsBoolean()
  @IsOptional()
  isCore?: boolean;

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @IsBoolean()
  @IsOptional()
  isAvailableInOnboarding?: boolean;

  // New dedicated fields extracted from metadata
  @IsEnum(ProductForms)
  @IsOptional()
  form?: ProductForms;

  @IsString()
  @IsOptional()
  tags?: string;

  @IsEnum(ProductTypes)
  @IsOptional()
  type?: ProductTypes;

  @IsString()
  @IsOptional()
  label?: string;

  @IsString()
  @IsOptional()
  onboardingLabel?: string;

  @IsNumber()
  @IsOptional()
  order?: number;

  @IsString()
  @IsOptional()
  notice?: string;

  @IsString()
  @IsOptional()
  supplyLength?: string;

  @IsNumber()
  @IsOptional()
  weightLossMultiplier?: number;

  @IsString()
  @IsOptional()
  customCard?: string;
}
