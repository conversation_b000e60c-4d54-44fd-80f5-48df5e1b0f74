import { UploadProductImageResponseDto } from '@modules/product/dto/upload-product-image-response.dto';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Pharmacy, Prisma, ProductForms, ProductTypes } from '@prisma/client';
import { Stripe } from 'stripe';

import { PrismaService } from '../../prisma/prisma.service';
import { StripeService } from '../../stripe/service/stripe.service';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';

@Injectable()
export class ProductService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly stripeService: StripeService,
  ) {}

  private async getProductCategoryByForm(form?: ProductForms): Promise<string> {
    // If no form is provided, default to injectable
    const formValue = form || ProductForms.injectable;

    // Try to find existing category by form
    let category = await this.prisma.productCategory.findFirst({
      where: { form: formValue },
    });

    // If not found, create it (this handles migration scenarios)
    if (!category) {
      category = await this.prisma.productCategory.create({
        data: {
          name: formValue,
          form: formValue,
          order: 0,
        },
      });
    }

    return category.id;
  }

  async findAll(options?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    direction?: 'asc' | 'desc';
    productCategoryId?: string;
  }) {
    const {
      page = 1,
      limit = 10,
      search = '',
      sortBy = '',
      direction = 'asc',
      productCategoryId,
    } = options || {};

    // Create the where clause for filtering
    let where: Prisma.ProductWhereInput = {};

    // Add productCategoryId filter if provided
    if (productCategoryId) {
      where.productCategories = {
        some: {
          productCategoryId: productCategoryId,
        },
      };
    }

    if (search) {
      where = {
        ...where,
        OR: [
          {
            name: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            description: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            pharmacy: {
              name: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
          },
        ],
      };
    }

    // Get total count for pagination
    const total = await this.prisma.product.count({ where });

    // Determine sort field and direction
    let orderBy: Prisma.ProductOrderByWithRelationInput[] = [];

    // Set the default sorting by pharmacy priority and then by product order
    if (!sortBy || sortBy === 'default') {
      orderBy = [
        { pharmacy: { regularPriority: 'desc' } }, // Sort by pharmacy priority (descending)
        { order: 'asc' }, // Then sort by product order (ascending)
      ];
    } else if (sortBy === 'name' || sortBy === 'description') {
      orderBy = [{ [sortBy]: direction }];
    } else if (sortBy === 'pharmacy') {
      orderBy = [{ pharmacy: { name: direction } }];
    } else if (sortBy === 'price') {
      orderBy = [{ defaultPrice: { unit_amount: direction } }];
    } else if (
      sortBy === 'active' ||
      sortBy === 'isCore' ||
      sortBy === 'isAvailableInOnboarding'
    ) {
      orderBy = [{ [sortBy]: direction }];
    }

    // Fetch paginated products
    const products = await this.prisma.product.findMany({
      where,
      include: {
        pharmacy: true,
        defaultPrice: true,
        productCategories: {
          include: {
            productCategory: true,
          },
        },
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });
    return {
      products,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    // First get the product details
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        pharmacy: true,
        defaultPrice: true,
        productCategories: {
          include: {
            productCategory: true,
          },
        },
        productPrice: {
          orderBy: { phase: 'asc' },
        },
      },
    });
    if (!product) {
      return null;
    }
    //

    // Check if any product prices are used in prescriptions
    const prescription = await this.prisma.prescription.findFirst({
      where: {
        productId: id,
      },
      select: { id: true },
    });

    // Add a flag indicating if the product can be deleted
    return {
      ...product,
      canBeDeleted: !prescription,
    };
  }

  /**
   * Helper function to fetch an image from a URL (Stripe) and upload it to S3
   * Returns the S3/CloudFront URL for permanent storage
   */
  private async fetchAndCacheImage(
    stripeImageUrl: string,
    productId: string,
  ): Promise<string | null> {
    if (!stripeImageUrl) return null;

    try {
      // Fetch the image from Stripe
      const { default: axios } = await import('axios');
      const response = await axios({
        url: stripeImageUrl,
        responseType: 'arraybuffer',
      });

      const imageBuffer = Buffer.from(response.data, 'binary');

      // Generate key using product ID
      const key = `${productId}.png`;

      // Determine content type based on URL
      let contentType = 'image/png';
      if (stripeImageUrl.endsWith('.jpg') || stripeImageUrl.endsWith('.jpeg')) {
        contentType = 'image/jpeg';
      } else if (stripeImageUrl.endsWith('.gif')) {
        contentType = 'image/gif';
      }

      // Upload to S3 and get the public URL
      const s3Url = await this.uploadImageToS3(key, imageBuffer, contentType);

      return s3Url;
    } catch (error) {
      console.error(
        `Error caching image from Stripe to S3 for product ${productId}:`,
        error,
      );
      // Return the original Stripe URL in case of error so we still have an image
      return stripeImageUrl;
    }
  }

  async create(data: CreateProductDto) {
    try {
      // Generate metadata from dedicated fields
      const metadata = {
        form: data.form,
        tags: data.tags,
        type: data.type,
        label: data.label,
        onboardingLabel: data.onboardingLabel,
        order: data.order,
        notice: data.notice,
        supplyLength: data.supplyLength,
        weightLossMultiplier: data.weightLossMultiplier,
        customCard: data.customCard,
      };

      // Create the product in Stripe
      const stripeProduct = await this.stripeService.client().products.create({
        name: data.name,
        description: data.description || undefined,
        active: data.active === undefined ? true : data.active,
        tax_code: 'txcd_32020001',
        // Include image URL if provided
        ...(data.image && { images: [data.image] }),
        metadata,
      });

      // Use the Stripe product ID if no ID is provided
      const productId = stripeProduct.id;

      // If there's an image URL from Stripe, fetch and cache it to S3
      let finalImageUrl = data.image;
      if (data.image) {
        const cachedUrl = await this.fetchAndCacheImage(data.image, productId);
        if (cachedUrl) {
          finalImageUrl = cachedUrl;
        }
      }

      // Create product first without categories
      const product = await this.prisma.product.create({
        data: {
          id: productId,
          pharmacyId: data.pharmacyId,
          name: data.name,
          description: data.description,
          image: finalImageUrl, // Use the S3 URL instead of the Stripe URL
          // Keep isCore in sync with type field
          isCore: data.type === 'core' ? true : (data.isCore ?? false),
          active: data.active,
          isAvailableInOnboarding: data.isAvailableInOnboarding,
          form: data.form,
          tags: data.tags,
          type: data.type,
          label: data.label,
          onboardingLabel: data.onboardingLabel,
          order: data.order,
          notice: data.notice,
          supplyLength: data.supplyLength,
          weightLossMultiplier: data.weightLossMultiplier,
          customCard: data.customCard,
          // Keep metadata in sync with dedicated fields
          metadata: metadata,
        },
      });

      // Connect categories if provided
      if (data.productCategoryIds && data.productCategoryIds.length > 0) {
        await this.prisma.productToProductCategory.createMany({
          data: data.productCategoryIds.map((categoryId) => ({
            productId: product.id,
            productCategoryId: categoryId,
          })),
        });
      }

      // Return the complete product with categories
      return this.prisma.product.findUnique({
        where: { id: product.id },
        select: {
          id: true,
          name: true,
          description: true,
          image: true,
          isCore: true,
          active: true,
          isAvailableInOnboarding: true,
          form: true,
          type: true,
          label: true,
          pharmacy: { select: { id: true, name: true } },
          productCategories: {
            include: {
              productCategory: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error creating product in Stripe:', error);
      throw new Error(`Failed to create product: ${error.message}`);
    }
  }

  async update(id: string, data: UpdateProductDto) {
    try {
      // Create a metadata object only from the fields that are defined in the data
      const metadataUpdate: Record<string, any> = {};
      if (data.form !== undefined) metadataUpdate.form = data.form;
      if (data.tags !== undefined) metadataUpdate.tags = data.tags;
      if (data.type !== undefined) metadataUpdate.type = data.type;
      if (data.label !== undefined) metadataUpdate.label = data.label;
      if (data.onboardingLabel !== undefined)
        metadataUpdate.onboardingLabel = data.onboardingLabel;
      if (data.order !== undefined) metadataUpdate.order = data.order;
      if (data.notice !== undefined) metadataUpdate.notice = data.notice;
      if (data.supplyLength !== undefined)
        metadataUpdate.supplyLength = data.supplyLength;
      if (data.weightLossMultiplier !== undefined)
        metadataUpdate.weightLossMultiplier = data.weightLossMultiplier;
      if (data.customCard !== undefined)
        metadataUpdate.customCard = data.customCard;

      // If there's an image URL, fetch and cache it to S3
      let finalImageUrl = data.image;
      if (data.image) {
        const cachedUrl = await this.fetchAndCacheImage(data.image, id);
        if (cachedUrl) {
          finalImageUrl = cachedUrl;
        }
      }

      // Update the product in Stripe with only the fields that are defined
      const stripeUpdateData: Record<string, any> = {};
      if (data.name !== undefined) stripeUpdateData.name = data.name;
      if (data.description !== undefined)
        stripeUpdateData.description = data.description;
      if (data.active !== undefined) stripeUpdateData.active = data.active;
      if (data.image !== undefined)
        stripeUpdateData.images = data.image ? [data.image] : [];

      // Only include metadata in the update if we have metadata fields to update
      if (Object.keys(metadataUpdate).length > 0) {
        stripeUpdateData.metadata = metadataUpdate;
      }

      await this.stripeService.client().products.update(id, stripeUpdateData);

      // Update the product in our database with only the fields that are defined
      const dbUpdateData: Record<string, any> = {};

      if (data.pharmacyId !== undefined)
        dbUpdateData.pharmacyId = data.pharmacyId;
      if (data.name !== undefined) dbUpdateData.name = data.name;
      if (data.description !== undefined)
        dbUpdateData.description = data.description;
      if (data.image !== undefined) dbUpdateData.image = finalImageUrl; // Use the S3 URL instead of the Stripe URL
      // Keep isCore in sync with type field
      if (data.type !== undefined) {
        dbUpdateData.isCore = data.type === 'core';
      } else if (data.isCore !== undefined) {
        dbUpdateData.isCore = data.isCore;
      }
      if (data.active !== undefined) dbUpdateData.active = data.active;
      if (data.isAvailableInOnboarding !== undefined)
        dbUpdateData.isAvailableInOnboarding = data.isAvailableInOnboarding;

      // Dedicated fields - only include if defined
      if (data.form !== undefined) {
        dbUpdateData.form = data.form;
      }

      // Note: Category updates handled separately after main update
      if (data.tags !== undefined) dbUpdateData.tags = data.tags;
      if (data.type !== undefined) dbUpdateData.type = data.type;
      if (data.label !== undefined) dbUpdateData.label = data.label;
      if (data.onboardingLabel !== undefined)
        dbUpdateData.onboardingLabel = data.onboardingLabel;
      if (data.order !== undefined) dbUpdateData.order = data.order;
      if (data.notice !== undefined) dbUpdateData.notice = data.notice;
      if (data.supplyLength !== undefined)
        dbUpdateData.supplyLength = data.supplyLength;
      if (data.weightLossMultiplier !== undefined)
        dbUpdateData.weightLossMultiplier = data.weightLossMultiplier;
      if (data.customCard !== undefined)
        dbUpdateData.customCard = data.customCard;

      // Only include metadata in the update if we have metadata fields to update
      if (Object.keys(metadataUpdate).length > 0) {
        dbUpdateData.metadata = metadataUpdate;
      }

      // Update the product
      await this.prisma.product.update({
        where: { id },
        data: dbUpdateData,
      });

      // Handle category updates if provided
      if (data.productCategoryIds !== undefined) {
        // Delete existing category connections
        await this.prisma.productToProductCategory.deleteMany({
          where: { productId: id },
        });

        // Create new category connections
        if (data.productCategoryIds.length > 0) {
          await this.prisma.productToProductCategory.createMany({
            data: data.productCategoryIds.map((categoryId) => ({
              productId: id,
              productCategoryId: categoryId,
            })),
          });
        }
      }

      // Return the updated product with categories
      return this.prisma.product.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          description: true,
          image: true,
          isCore: true,
          active: true,
          isAvailableInOnboarding: true,
          form: true,
          type: true,
          label: true,
          pharmacy: { select: { id: true, name: true } },
          productCategories: {
            include: {
              productCategory: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error updating product in Stripe:', error);
      throw new Error(`Failed to update product: ${error.message}`);
    }
  }

  async delete(id: string) {
    try {
      // First check if the product has any associated prices
      const associatedPrices = await this.prisma.productPrice.findMany({
        where: { productId: id },
        select: { id: true, name: true },
      });

      // If there are associated prices, throw an error
      if (associatedPrices.length > 0) {
        const priceNames = associatedPrices
          .map((price) => price.name)
          .join(', ');
        throw new Error(
          `Cannot delete product with associated prices. Please delete the following prices first: ${priceNames}`,
        );
      }

      // Try to delete the product in Stripe
      try {
        await this.stripeService.client().products.del(id);
      } catch (stripeError) {
        // If we can't delete it, try to archive it instead
        if (stripeError.statusCode === 400) {
          await this.stripeService
            .client()
            .products.update(id, { active: false });
        } else {
          throw stripeError;
        }
      }

      // Delete from our database
      return this.prisma.product.delete({
        where: { id },
      });
    } catch (error) {
      console.error('Error deleting product:', error);

      // If we can't delete from Stripe but still want to mark as inactive in our DB
      if (
        error.message.includes('cannot be deleted') ||
        error.statusCode === 400
      ) {
        console.log(
          `Product ${id} could not be deleted in Stripe, marking as inactive in our database`,
        );
        return this.prisma.product.update({
          where: { id },
          data: { active: false },
        });
      }

      throw new Error(`Failed to delete product: ${error.message}`);
    }
  }

  /**
   * Helper method to upload an image to S3 and return the public URL
   * This is used by the uploadImage method and ensures we cache images for public use
   * rather than serving them directly from Stripe
   */
  private async uploadImageToS3(
    key: string,
    imageBuffer: Buffer,
    contentType: string,
  ): Promise<string> {
    try {
      const { S3 } = await import('aws-sdk');
      const s3 = new S3();
      const bucket = process.env.AWS_S3_PRODUCTS_PHOTOS_BUCKETNAME;

      // Calculate MD5 hash for content verification
      const { createHash } = await import('crypto');
      const contentMD5 = createHash('md5').update(imageBuffer).digest('base64');

      // Check if image already exists and is identical
      try {
        const headObjectParams = { Bucket: bucket, Key: key };
        const existingObject = await s3.headObject(headObjectParams).promise();

        const etag = existingObject.ETag.replace(/"/g, '');
        const imageHashMD5 = createHash('md5')
          .update(imageBuffer)
          .digest('hex');

        if (etag === imageHashMD5) {
          console.debug(`Image with key ${key} is already up to date in S3`);
          // Image exists and is identical, return the URL
          const baseUrl = process.env.CLOUDFRONT_PRODUCTS_PHOTOS_URL;
          return baseUrl
            ? `${baseUrl}/${key}`
            : `https://${bucket}.s3.amazonaws.com/${key}`;
        }
      } catch (error) {
        if (error.code !== 'NotFound') {
          throw error;
        }
        // NotFound errors are expected when the object doesn't exist yet
      }

      // Upload to S3
      await s3
        .putObject({
          Bucket: bucket,
          Key: key,
          Body: imageBuffer,
          ContentType: contentType,
          ContentMD5: contentMD5,
        })
        .promise();

      console.log(`Successfully uploaded image with key ${key} to S3`);

      // Format the URL using CloudFront if configured, otherwise use S3 direct URL
      const baseUrl = process.env.CLOUDFRONT_PRODUCTS_PHOTOS_URL;
      return baseUrl
        ? `${baseUrl}/${key}`
        : `https://${bucket}.s3.amazonaws.com/${key}`;
    } catch (error) {
      console.error(`Error uploading image to S3 with key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Uploads a product image to Stripe and returns the Stripe URL
   * This URL is used temporarily in the frontend for preview purposes
   * The actual S3 upload will happen during product creation/update
   *
   * @param file The uploaded file
   */
  async uploadImage(
    file: Express.Multer.File,
  ): Promise<UploadProductImageResponseDto> {
    try {
      // Upload to Stripe and get a file link for preview
      const uploadedFile = await this.stripeService.client().files.create({
        purpose: 'business_logo', // Use correct purpose for product images
        file: {
          data: file.buffer,
          name: file.originalname,
          type: file.mimetype,
        },
      });

      // Create a file link to make it publicly accessible for preview
      const fileLink = await this.stripeService.client().fileLinks.create({
        file: uploadedFile.id,
      });

      // Store the file buffer in memory cache for later use during product create/update
      // In a production environment, you might want to use a more robust caching mechanism
      // like Redis or a temporary file storage

      // Return the Stripe URL which will be used temporarily for preview
      return { url: fileLink.url };
    } catch (error) {
      console.error('Error uploading product image to Stripe:', error);
      throw new BadRequestException(
        `Failed to upload image: ${error.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * Synchronize products from Stripe to the database
   * @param newOnly If true, only sync new products that don't exist in the database
   * @returns Summary data of the synchronization process
   */
  async syncProductsFromStripe(newOnly: boolean = false) {
    const { products, prices } = await this.stripeService.getProducts();
    const summary = {
      mode: newOnly ? 'new-only' : 'full-sync',
      newProductIds: [],
      newPriceIds: [],
      updatedProductIds: [],
      updatedPriceIds: [],
      deactivatedProducts: [],
      deactivatedPrices: [],
      updatedDefaultPrices: 0,
      totalProductsProcessed: products.data.length,
      totalPricesProcessed: prices.data.length,
    };

    // 1. Sync products - now using metadata.pharmacy to find the correct pharmacy
    // Track the IDs of newly created products
    const syncProductsResult = await this.syncProducts(products.data, newOnly);
    summary.newProductIds = syncProductsResult.newProductIds;
    summary.updatedProductIds = syncProductsResult.updatedProductIds;

    // 2. Sync prices - now also syncs new prices for existing products when in newOnly mode
    const syncPricesResult = await this.syncPrices(prices.data, newOnly);
    summary.newPriceIds = syncPricesResult.newPriceIds;
    summary.updatedPriceIds = syncPricesResult.updatedPriceIds;

    if (newOnly) {
      // Update default prices only for newly created products
      const updateResult = await this.updateProductDefaultPrices(
        products.data.filter((product) =>
          summary.newProductIds.includes(product.id),
        ),
      );
      summary.updatedDefaultPrices = updateResult.updatedCount;
    } else {
      // 3. Update default prices for all products
      const updateResult = await this.updateProductDefaultPrices(products.data);
      summary.updatedDefaultPrices = updateResult.updatedCount;

      // 4. Deactivate products that exist in DB but not in Stripe
      const deactivatedProductsResult = await this.deactivateStaleProducts(
        products.data,
      );
      summary.deactivatedProducts =
        deactivatedProductsResult.deactivatedProducts;

      // 5. Deactivate prices that exist in DB but not in Stripe
      const deactivatedPricesResult = await this.deactivateStalePrices(
        prices.data,
      );
      summary.deactivatedPrices = deactivatedPricesResult.deactivatedPrices;
    }

    return summary;
  }

  /**
   * Get the default pharmacy (highest priority enabled pharmacy)
   */
  private async getDefaultPharmacy() {
    return this.prisma.pharmacy.findFirstOrThrow({
      where: { enabled: true },
      orderBy: { regularPriority: 'desc' },
    });
  }

  /**
   * Find a pharmacy by its DoseSpot ID
   */
  private async findPharmacyByDoseSpotId(doseSpotPharmacyId: string) {
    try {
      return await this.prisma.pharmacy.findFirstOrThrow({
        where: { doseSpotPharmacyId },
      });
    } catch (error) {
      console.warn(
        `Pharmacy with doseSpotPharmacyId ${doseSpotPharmacyId} not found.`,
      );
      return null;
    }
  }

  /**
   * Synchronize products from Stripe to the database
   * @param products List of products from Stripe
   * @param newOnly If true, only sync new products that don't exist in the database
   * @returns Object containing lists of newly created and updated product IDs
   */
  private async syncProducts(
    products: Stripe.Product[],
    newOnly: boolean = false,
  ) {
    // Track newly created and updated products
    const newProductIds: string[] = [];
    const updatedProductIds: string[] = [];
    const skippedProducts: Array<{ id: string; reason: string }> = [];

    for (const product of products) {
      // Check if product exists in the database
      const existingProduct = await this.prisma.product.findUnique({
        where: { id: product.id },
      });

      // If newOnly flag is set and the product already exists, skip it
      if (newOnly && existingProduct) {
        skippedProducts.push({
          id: product.id,
          reason: `Skipping existing product in new-only mode: ${product.name}`,
        });
        continue;
      }

      // If product is not active and doesn't exist in DB, skip it
      if (!product.active && !existingProduct) {
        skippedProducts.push({
          id: product.id,
          reason: `Skipping inactive product that doesn't exist in DB: ${product.name}`,
        });
        continue;
      }

      // Skip if product has a 'skip' key in metadata, regardless of its value
      if (product.metadata && 'skip' in product.metadata) {
        skippedProducts.push({
          id: product.id,
          reason: `Skipping product with 'skip' flag in metadata: ${product.name}`,
        });
        continue;
      }

      // Get the correct pharmacy based on metadata.pharmacy (doseSpotPharmacyId)
      let pharmacy: Pharmacy | null = null;
      if (product.metadata?.pharmacy) {
        pharmacy = await this.findPharmacyByDoseSpotId(
          product.metadata.pharmacy,
        );

        // Skip creating the product if pharmacy cannot be found by DoseSpot ID
        if (!pharmacy && !existingProduct) {
          skippedProducts.push({
            id: product.id,
            reason: `Skipping product creation because pharmacy with doseSpotPharmacyId ${product.metadata.pharmacy} not found: ${product.name}`,
          });
          continue;
        }
      }

      // For existing products or new products without pharmacy metadata, use default pharmacy
      if (!pharmacy) {
        pharmacy = await this.getDefaultPharmacy();
      }

      const imageUrl = product.images?.[0];
      let finalImageUrl = null;

      if (imageUrl) {
        // Use our existing image caching logic
        const cachedUrl = await this.fetchAndCacheImage(imageUrl, product.id);
        if (cachedUrl) {
          finalImageUrl = cachedUrl;
        }
      }

      // Get or create the product category based on form
      const form =
        (product.metadata?.form as ProductForms) || ProductForms.injectable;
      const productCategoryId = await this.getProductCategoryByForm(form);

      // Extract metadata fields to map to database columns
      const productData: Prisma.ProductCreateInput = {
        id: product.id,
        name: product.name,
        image: finalImageUrl,
        description: product.description || '',
        active: product.active, // Always set the active status to match Stripe
        metadata: product.metadata,
        pharmacy: { connect: { id: pharmacy.id } },
        // Map metadata fields to database columns
        // Keep isCore in sync with type field
        isCore: product.metadata?.type === 'core',
        form: product.metadata?.form as ProductForms,
        tags: product.metadata?.tags,
        type: product.metadata?.type as ProductTypes,
        label: product.metadata?.label,
        onboardingLabel: product.metadata?.onboardingLabel,
        order: product.metadata?.order
          ? parseInt(product.metadata.order)
          : undefined,
        notice: product.metadata?.notice,
        supplyLength: product.metadata?.supplyLength,
        weightLossMultiplier: product.metadata?.weightLossMultiplier
          ? parseFloat(product.metadata.weightLossMultiplier)
          : undefined,
        customCard: product.metadata?.customCard,
        // Default value for isAvailableInOnboarding if not specified
        isAvailableInOnboarding:
          product.metadata?.isAvailableInOnboarding !== 'false',
      };

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { pharmacy: _, ...updateData } = productData;

      if (existingProduct) {
        // Only update if not in new-only mode
        if (!newOnly) {
          await this.prisma.product.update({
            where: { id: product.id },
            data: updateData,
          });

          // Update category connection if needed
          const existingCategories =
            await this.prisma.productToProductCategory.findMany({
              where: { productId: product.id },
            });

          if (existingCategories.length === 0 && productCategoryId) {
            await this.prisma.productToProductCategory.create({
              data: {
                productId: product.id,
                productCategoryId: productCategoryId,
              },
            });
          }

          // Track status changes
          if (existingProduct.active !== product.active) {
            updatedProductIds.push(product.id);
          } else {
            updatedProductIds.push(product.id);
          }
        }
      } else {
        // Always create new products
        await this.prisma.product.create({
          data: productData,
        });

        // Create category connection
        if (productCategoryId) {
          await this.prisma.productToProductCategory.create({
            data: {
              productId: product.id,
              productCategoryId: productCategoryId,
            },
          });
        }

        // Track newly created product IDs
        newProductIds.push(product.id);
      }
    }

    // Return the results
    return {
      newProductIds,
      updatedProductIds,
      skippedProducts,
    };
  }

  /**
   * Synchronize prices from Stripe to the database
   * @param prices List of prices from Stripe
   * @param newOnly If true, only sync new prices that don't exist in the database
   * @returns Object containing lists of newly created and updated price IDs
   */
  private async syncPrices(prices: Stripe.Price[], newOnly: boolean = false) {
    const newPriceIds: string[] = [];
    const updatedPriceIds: string[] = [];
    const skippedPrices: Array<{ id: string; reason: string }> = [];

    for (const price of prices) {
      // Check if price exists in the database
      const existingPrice = await this.prisma.productPrice.findUnique({
        where: { id: price.id },
      });

      // If price exists in DB and we're in newOnly mode, skip it
      if (newOnly && existingPrice) {
        skippedPrices.push({
          id: price.id,
          reason: 'Skipping existing price in new-only mode',
        });
        continue;
      }

      // If price is not active and doesn't exist in DB, skip it
      if (!price.active && !existingPrice) {
        skippedPrices.push({
          id: price.id,
          reason: "Skipping inactive price that doesn't exist in DB",
        });
        continue;
      }

      // Skip if price has a 'skip' key in metadata, regardless of its value
      if (price.metadata && 'skip' in price.metadata) {
        skippedPrices.push({
          id: price.id,
          reason: "Skipping price with 'skip' flag in metadata",
        });
        continue;
      }

      // Check if the associated product exists in our database
      const productId = price.product as string;
      const productExists = await this.prisma.product.findUnique({
        where: { id: productId },
      });

      // Skip if the associated product doesn't exist
      if (!productExists) {
        skippedPrices.push({
          id: price.id,
          reason: `Skipping price creation because associated product ${productId} not found`,
        });
        continue;
      }

      // Use price.nickname or provide a fallback name
      const priceName = price.nickname || `Price for ${price.product}`;

      // Extract metadata fields to map to database columns
      const priceData: Prisma.ProductPriceCreateInput = {
        id: price.id,
        name: priceName,
        active: price.active, // Always set the active status to match Stripe
        unit_amount: price.unit_amount || 0,
        metadata: price.metadata,
        product: { connect: { id: productId } },
        // Map metadata fields to database columns
        description: price.metadata?.description,
        dosageDescription: price.metadata?.dosageDescription,
        dosageLabel: price.metadata?.dosageLabel,
        dosageTimeframe: price.metadata?.dosageTimeframe,
        label: price.metadata?.label,
        milligrams: price.metadata?.milligrams
          ? parseFloat(price.metadata.milligrams)
          : undefined,
        phase: price.metadata?.phase ? parseInt(price.metadata.phase) : 1,
      };

      if (existingPrice) {
        // Only update if not in new-only mode
        if (!newOnly) {
          await this.prisma.productPrice.update({
            where: { id: price.id },
            data: priceData,
          });

          // Track changes in active status
          if (existingPrice.active !== price.active) {
            updatedPriceIds.push(price.id);
          } else {
            updatedPriceIds.push(price.id);
          }
        }
      } else {
        // Always create new prices, regardless of whether the product is new or existing
        await this.prisma.productPrice.create({
          data: priceData,
        });
        newPriceIds.push(price.id);
      }
    }

    return {
      newPriceIds,
      updatedPriceIds,
      skippedPrices,
    };
  }

  /**
   * Update the default price for products based on Stripe data
   * @param products List of products from Stripe
   * @returns Object containing count of updated default prices and skipped products
   */
  private async updateProductDefaultPrices(products: Stripe.Product[]) {
    let updatedCount = 0;
    const skippedProducts: Array<{ id: string; reason: string }> = [];

    for (const product of products) {
      const defaultPrice = product.default_price as Stripe.Price | null;
      if (!defaultPrice) {
        skippedProducts.push({
          id: product.id,
          reason: 'Product has no default price',
        });
        continue;
      }

      // Make sure the price exists in the database
      const priceExists = await this.prisma.productPrice.findUnique({
        where: { id: defaultPrice.id },
      });

      if (!priceExists) {
        skippedProducts.push({
          id: product.id,
          reason: `Default price ${defaultPrice.id} not found in the database`,
        });
        continue;
      }

      await this.prisma.product.update({
        where: { id: product.id },
        data: { defaultPriceId: defaultPrice.id },
      });

      updatedCount++;
    }

    return {
      updatedCount,
      skippedProducts,
    };
  }

  /**
   * Deactivate products that exist in the database but no longer exist in Stripe
   * @param stripeProducts List of products from Stripe
   * @returns Object containing list of deactivated products
   */
  private async deactivateStaleProducts(stripeProducts: Stripe.Product[]) {
    // Get all active product IDs from Stripe
    const stripeProductIds = stripeProducts.map((product) => product.id);

    // Find all products in our DB that are not in the Stripe list
    const staleProducts = await this.prisma.product.findMany({
      where: {
        active: true,
        id: { notIn: stripeProductIds },
      },
      include: {
        defaultPrice: true,
      },
    });

    if (staleProducts.length === 0) {
      return { deactivatedProducts: [] };
    }

    // Build deactivated products list
    const deactivatedProducts = staleProducts.map((product) => ({
      id: product.id,
      name: product.name,
      price: product.defaultPrice?.unit_amount || 0,
    }));

    // Update the stale products to inactive
    for (const product of staleProducts) {
      await this.prisma.product.update({
        where: { id: product.id },
        data: { active: false },
      });
    }

    return { deactivatedProducts };
  }

  /**
   * Deactivate prices that exist in the database but no longer exist in Stripe
   * @param stripePrices List of prices from Stripe
   * @returns Object containing list of deactivated prices
   */
  private async deactivateStalePrices(stripePrices: Stripe.Price[]) {
    // Get all price IDs from Stripe
    const stripePriceIds = stripePrices.map((price) => price.id);

    // Find all active prices in our DB that are not in the Stripe list
    const stalePrices = await this.prisma.productPrice.findMany({
      where: {
        active: true,
        id: { notIn: stripePriceIds },
      },
      include: {
        product: true,
      },
    });

    if (stalePrices.length === 0) {
      return { deactivatedPrices: [] };
    }

    // Build deactivated prices list
    const deactivatedPrices = stalePrices.map((price) => ({
      id: price.id,
      name: price.name,
      productName: price.product?.name || 'Unknown Product',
      amount: price.unit_amount,
    }));

    // Update the stale prices to inactive
    for (const price of stalePrices) {
      await this.prisma.productPrice.update({
        where: { id: price.id },
        data: { active: false },
      });
    }

    return { deactivatedPrices };
  }
}
