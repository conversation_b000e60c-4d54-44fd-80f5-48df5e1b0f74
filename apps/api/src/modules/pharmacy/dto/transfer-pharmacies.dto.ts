import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ot<PERSON>mpt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>S<PERSON>,
  IsUUI<PERSON>,
} from 'class-validator';

export class TransferPharmaciesDto {
  /**
   * Source pharmacy ID whose patients will be transferred
   */
  @IsUUID()
  @IsNotEmpty()
  sourcePharmacyId: string;

  /**
   * Target pharmacy ID to which patients will be transferred
   */
  @IsUUID()
  @IsNotEmpty()
  targetPharmacyId: string;

  /**
   * Array of state IDs to include in the transfer
   * Only patients in these states will be transferred
   */
  @IsArray()
  @IsUUID(undefined, { each: true })
  @IsNotEmpty()
  stateIds: string[];

  /**
   * Array of medication forms to include in the transfer
   * Only treatments with these forms will be transferred
   */
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  forms?: string[];

  /**
   * Optional reason for the transfer
   */
  @IsString()
  @IsOptional()
  reason?: string;
}
