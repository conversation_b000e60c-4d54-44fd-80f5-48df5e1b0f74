import { forwardRef, Module } from '@nestjs/common';

import { AppCacheModule } from '../cache/cache.module';
import { ChatModule } from '../chat/chat.module';
import { DosespotModule } from '../dosespot/dosespot.module';
import { PrismaModule } from '../prisma/prisma.module';
import { SqsModule } from '../shared/aws/sqs/sqs.module';
import { OrchestrationModule } from '../shared/orchestration/orchestration.module';
import { SharedModule } from '../shared/shared.module';
import { TreatmentModule } from '../treatment/treatment.module';
import { PharmacyTransferConsumer } from './pharmacy-transfer.consumer';
import { PharmacyTransferService } from './pharmacy-transfer.service';
import { PharmacyController } from './pharmacy.controller';
import { PharmacyService } from './pharmacy.service';

@Module({
  imports: [
    PrismaModule,
    AppCacheModule,
    DosespotModule,
    forwardRef(() => ChatModule),
    forwardRef(() => TreatmentModule),
    SharedModule,
    SqsModule,
    OrchestrationModule,
  ],
  controllers: [PharmacyController],
  providers: [
    PharmacyService,
    PharmacyTransferService,
    PharmacyTransferConsumer,
  ],
  exports: [PharmacyService, PharmacyTransferService],
})
export class PharmacyModule {}
