import { AuditService } from '@/modules/audit-log/audit-log.service';
import { OnboardingEventEmitterService } from '@/modules/onboarding/use-cases/onboarding-event-emitter.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { segmentIdentifyEvent } from '@/modules/shared/events';
import { SegmentIdentify } from '@/modules/shared/types/events';
import { PatientPaymentMethodPersistence } from '@adapters/persistence/database/patient-payment-method.persistence';
import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { ShippingAddressPersistence } from '@adapters/persistence/database/shipping-address.persistence';
import { StatePersistence } from '@adapters/persistence/database/state.persistence';
import {
  OnboardingActor,
  OnboardingSnapshot,
  OnboardingStateService,
} from '@modules/onboarding/onboarding-state.service';
import { ShippingInfo } from '@modules/onboarding/types/shipping-info.type';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { patientGender, patientStatus, Prisma } from '@prisma/client';
import { Stripe } from 'stripe';

type PatientOboardingProfile = Awaited<
  ReturnType<PatientPersistence['getOnboardingProfileByStripeCustomer']>
>;

@Injectable()
export class StripeAttemptOnboadingPaymentValidationUseCase {
  constructor(
    private readonly patientPersistence: PatientPersistence,
    private readonly statePersistence: StatePersistence,
    private readonly shippingAddressPersistence: ShippingAddressPersistence,
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPaymentMethodPersistence: PatientPaymentMethodPersistence,
    private readonly prismaService: PrismaService,
    private readonly onboardingEventEmitterService: OnboardingEventEmitterService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async execute(event: Stripe.PaymentMethodAttachedEvent) {
    let profile: PatientOboardingProfile;

    try {
      profile =
        await this.patientPersistence.getOnboardingProfileByStripeCustomer(
          event.data.object.customer as string,
        );
    } catch (error) {
      console.error('Error fetching patient profile:', error);
      return;
    }

    // if user aleady completed onboarding, skip silently
    if (profile.status !== 'onboardingPending') return;

    try {
      const onboarding = await this.onboardingService.getCurrentOnboardingActor(
        profile.questionnaire.version,
        profile.onboardingState as unknown as OnboardingSnapshot,
      );
      const snapshot = onboarding.getSnapshot();

      if (snapshot.value !== 'payment') {
        throw new Error('Invalid onboarding state');
      }

      await this.handleOnboarding(profile, onboarding, event);
    } catch (error) {
      console.error('Error fetching patient profile:', error);
      throw new Error(
        `Failed to handle payment method attached for patient ${profile.id}: ${error.message}`,
      );
    }
  }

  private async handleOnboarding(
    profile: PatientOnboardingProfile,
    onboarding: OnboardingActor,
    event: Stripe.PaymentMethodAttachedEvent,
  ) {
    try {
      await this.prismaService.$transaction(async (prisma) => {
        // perform complete transition and get context data
        const actor = this.onboardingService.performTransition(
          onboarding,
          'complete',
        );
        const snapshot = actor.getSnapshot();
        const context = snapshot.context;

        // persist desired products
        const desiredTreatments = context.products.map((product) => ({
          productId: product.id,
          vials: product.vials,
        }));
        await this.patientPersistence.update(
          profile.id,
          {
            desiredTreatments: {
              create: desiredTreatments,
            },
          },
          prisma,
        );

        const state = await this.statePersistence.getByCode(
          context.shippingInfo.state,
        );
        const shippingStateChanged = state.id !== profile.state.id;

        // persist shipping address
        const shipping: ShippingInfo = {
          address1: context.shippingInfo.address1,
          address2: context.shippingInfo.address2,
          city: context.shippingInfo.city,
          stateId: state.id,
          zip: context.shippingInfo.zip.toString(),
        };
        await this.shippingAddressPersistence.create(
          profile.id,
          shipping,
          prisma,
        );

        // persist payment method
        await this.patientPaymentMethodPersistence.create({
          patient: {
            connect: { id: profile.id },
          },
          stripeId: event.data.object.id,
          data: event.data.object as any,
          type: event.data.object.type,
          default: true,
        });

        // update patient profile
        const questionnaire = context.questionnaire;
        const birthDate = new Date(questionnaire['birthDate']);
        const gender = questionnaire['gender'] as patientGender;
        const height = questionnaire['height'];
        const weight = questionnaire['weight'];
        const idPhoto = context['id-photo'];
        const facePhoto = context['face-photo'];

        const status: patientStatus =
          idPhoto && facePhoto ? 'onboardingCompleted' : 'pendingUploadPhotos';

        // Build update data including pharmacy if override exists and state if changed
        const updateData: Omit<Prisma.PatientUpdateInput, 'onboardingState'> = {
          status,
          birthDate,
          gender,
          height,
          weight,
          idPhoto,
          facePhoto,
          completedAt: new Date(),
        };

        // Add pharmacy update if override exists in context
        if (context.pharmacyId && context.pharmacyId !== profile.pharmacy?.id) {
          Object.assign(updateData, {
            pharmacy: { connect: { id: context.pharmacyId } },
          });
        }

        if (shippingStateChanged) {
          Object.assign(updateData, { state: { connect: { id: state.id } } });

          // Emit segment identify event for state change
          const identifyEvent: SegmentIdentify = {
            userId: profile.id,
            traits: { state: context.shippingInfo.state },
          };
          this.eventEmitter.emit(
            segmentIdentifyEvent.analyticIdentify,
            identifyEvent,
          );
        }

        await this.patientPersistence.updateOnboarding(
          profile.id,
          onboarding.getSnapshot(),
          updateData,
          prisma,
        );

        void this.onboardingEventEmitterService.execute(profile, snapshot);
      });
    } catch (e) {
      console.error(e);
      throw e;
    }
  }
}
