import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import Stripe from 'stripe';

@Injectable()
export class CustomerSaveNewPaymentMethodUseCase {
  private readonly logger = new Logger(
    CustomerSaveNewPaymentMethodUseCase.name,
  );

  constructor(
    private readonly prismaService: PrismaService,
    private readonly patientPaymentMethodPersistence: PatientPaymentMethodPersistence,
  ) {}
  async execute(patientId, paymentMethod: Stripe.PaymentMethod) {
    if (paymentMethod.type !== 'card') return;

    await runInDbTransaction(this.prismaService, async (prisma) => {
      await this.patientPaymentMethodPersistence.create(
        {
          patient: {
            connect: { id: patientId },
          },
          stripeId: paymentMethod.id,
          data: paymentMethod,
          type: paymentMethod.type,
          default: true,
        },
        { prisma },
      );
    });
  }
}
