import * as process from 'process';
import { StripeService } from '@modules/stripe/service/stripe.service';
import { StripeAttemptOnboadingPaymentValidationUseCase } from '@modules/stripe/use-cases/payment-method-attached.use-case';
import {
  Controller,
  Get,
  Headers,
  Inject,
  NotFoundException,
  Param,
  Post,
  RawBodyRequest,
  Req,
  Res,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Request, Response } from 'express';
import Stripe from 'stripe';

import { PrismaService } from '../prisma/prisma.service';
import { OutboxerService } from '../shared/outboxer/outboxer.service';
import { StripeChargeDisputeCreatedUseCase } from './use-cases/charge-dispute-created.use-case';

const STRIPE_EVENTS_WHITELIST: Stripe.Event['type'][] = [
  'invoice.finalized',
  'invoice.paid',
  'invoice.payment_failed',
  'invoice.marked_uncollectible',
  'invoice.voided',
  'charge.refunded',
  'charge.dispute.created',
  'payment_method.attached',
  'customer.updated',
] as const;

@Controller('stripe')
export class StripeController {
  private readonly client: Stripe;

  constructor(
    private readonly prisma: PrismaService,
    private readonly outboxer: OutboxerService,
    private readonly eventEmitter: EventEmitter2,
    private readonly stripePaymentMethodAttachedUseCase: StripeAttemptOnboadingPaymentValidationUseCase,
    private readonly chargeDisputeCreatedUseCase: StripeChargeDisputeCreatedUseCase,
    @Inject(ConfigService) private readonly config: ConfigService,
    private readonly stripeService: StripeService,
  ) {
    this.client = new Stripe(process.env.STRIPE_SECRET_KEY, {
      typescript: true,
    });
  }

  @Post('webhook')
  async webhook(
    @Headers('stripe-signature') sig: string,
    @Req() req: RawBodyRequest<Request>,
    @Res() res: Response,
  ) {
    let event: Stripe.Event;

    try {
      event = this.client.webhooks.constructEvent(
        req.rawBody,
        sig,
        this.config.get('STRIPE_WEBHOOK_SECRET'),
      );
    } catch (err) {
      // On error, log and return the error message
      console.log(`Error message: ${err.message}`);
      res.status(400).send(`Webhook Error: ${err.message}`);
      return;
    }

    console.log('-----Stripe:', event.type, event.id);

    if (!STRIPE_EVENTS_WHITELIST.includes(event.type)) {
      console.warn(
        `Unhandled Stripe event type: ${event.type}, id: ${event.id}`,
      );
      res.status(200).json({ received: true });
      return;
    }

    const patientInfo = await this.getPatientInfo(event);

    if (!patientInfo) {
      console.warn(
        `Patient not found for event: ${event.type}, id: ${event.id}`,
      );
      res.status(500).json({ error: 'patient not found' });
      return;
    }

    const patientId = patientInfo.id;

    switch (event.type) {
      case 'invoice.finalized':
        await this.outboxer.enqueue(patientId, 'stripe-invoice-updated', {
          event: 'finalized',
          patientId,
          invoice: event.data.object as Stripe.Invoice,
        });
        break;
      case 'invoice.paid':
        await this.outboxer.enqueue(patientId, 'stripe-invoice-updated', {
          event: 'paid',
          patientId,
          invoice: event.data.object as Stripe.Invoice,
        });
        break;
      case 'invoice.payment_failed':
        await this.outboxer.enqueue(patientId, 'stripe-invoice-updated', {
          event: 'payment_failed',
          patientId,
          invoice: event.data.object as Stripe.Invoice,
        });
        break;
      case 'invoice.marked_uncollectible':
        await this.outboxer.enqueue(patientId, 'stripe-invoice-updated', {
          event: 'uncollectible',
          patientId,
          invoice: event.data.object,
        });
        break;
      case 'invoice.voided':
        await this.outboxer.enqueue(patientId, 'stripe-invoice-updated', {
          event: 'voided',
          patientId,
          invoice: event.data.object,
        });
        break;
      case 'charge.refunded':
        await this.outboxer.enqueue(patientId, 'stripe-charge-updated', {
          event: 'refunded',
          patientId,
          charge: event.data.object,
        });
        break;
      case 'charge.dispute.created':
        await this.chargeDisputeCreatedUseCase.execute(event);
        break;
      case 'payment_method.attached':
        await this.stripePaymentMethodAttachedUseCase.execute(event);
        await this.outboxer.enqueue(patientId, 'stripe-customer-updated', {
          event: 'payment-method-attached',
          patientId,
          customerId: event.data.object.customer as string,
          paymentMethod: event.data.object,
        });
        break;
      case 'customer.updated':
        await this.prisma.$transaction(async (prisma) => {
          const customerId = event.data.object.id;
          const previousAttributes = event.data.previous_attributes ?? {};
          if (
            Object.hasOwn(
              previousAttributes?.invoice_settings ?? {},
              'default_payment_method',
            )
          ) {
            await this.outboxer.enqueue(
              patientId,
              'stripe-customer-updated',
              {
                event: 'default-payment-method-updated',
                patientId,
                customerId,
                customer: event.data.object,
              },
              { prisma },
            );
          }
        });
        break;

      default:
        console.warn(`Unhandled event type: ${event.type}`);
    }
    // Return a response to acknowledge receipt of the event
    res.status(200).json({ received: true });
  }

  @Get('coupon/:promoCoupon/validate')
  async validatePromoCoupon(@Param('promoCoupon') promoCoupon: string) {
    try {
      const { valid, percent_off, id } =
        await this.stripeService.getCoupon(promoCoupon);
      return { valid, percent_off, id };
    } catch (e) {
      throw new NotFoundException(e.message);
    }
  }

  private async getPatientInfo(event: Stripe.Event) {
    let email: string;
    let stripeCustomerId: string;

    switch (event.type) {
      case 'invoice.finalized':
      case 'invoice.paid':
      case 'invoice.payment_failed':
      case 'invoice.marked_uncollectible':
      case 'invoice.voided':
      case 'charge.refunded':
      case 'payment_method.attached':
        stripeCustomerId = event.data.object.customer as string;
        break;
      case 'charge.dispute.created':
        email = event.data.object.evidence.customer_email_address;
        break;
      case 'customer.created':
      case 'customer.updated':
        stripeCustomerId = event.data.object.id;
        break;
      default:
        return null;
    }

    return this.prisma.patient.findFirst({
      where: { stripeCustomerId, user: { email } },
      select: { id: true },
    });
  }
}
