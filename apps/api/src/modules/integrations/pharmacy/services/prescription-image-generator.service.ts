import * as path from 'path';
import type {
  PharmacyPrescriptionProduct,
  PrescriptionRequest,
} from '@modules/integrations/pharmacy';
import type { CanvasRenderingContext2D } from 'canvas';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createCanvas, registerFont } from 'canvas';
import { format } from 'date-fns';

@Injectable()
export class PrescriptionImageGeneratorService {
  private readonly logger = new Logger(PrescriptionImageGeneratorService.name);
  private readonly resourcesPath: string;

  constructor(private readonly configService: ConfigService) {
    this.resourcesPath = this.configService.get<string>('resourcesPath');

    // Register fonts
    try {
      const creattionFontPath = path.join(this.resourcesPath, 'Creattion.otf');
      registerFont(creattionFontPath, { family: 'Creattion' });
      this.logger.log('Registered Creattion font for prescriptions');
    } catch (error) {
      this.logger.warn(
        'Failed to register Creattion font, will use fallback',
        error,
      );
    }

    try {
      const liberationSansRegularPath = path.join(
        this.resourcesPath,
        'LiberationSans-Regular.ttf',
      );
      registerFont(liberationSansRegularPath, { family: 'Liberation Sans' });
      this.logger.log(
        'Registered Liberation Sans Regular font for prescriptions',
      );
    } catch (error) {
      this.logger.warn(
        'Failed to register Liberation Sans Regular font, will use fallback',
        error,
      );
    }

    try {
      const liberationSansBoldPath = path.join(
        this.resourcesPath,
        'LiberationSans-Bold.ttf',
      );
      registerFont(liberationSansBoldPath, {
        family: 'Liberation Sans',
        weight: 'bold',
      });
      this.logger.log('Registered Liberation Sans Bold font for prescriptions');
    } catch (error) {
      this.logger.warn(
        'Failed to register Liberation Sans Bold font, will use fallback',
        error,
      );
    }
  }

  async generatePrescriptionImage(
    request: PrescriptionRequest,
    product: PharmacyPrescriptionProduct,
  ): Promise<string> {
    try {
      // Create canvas with simple prescription format
      const width = 600;

      // Pre-calculate the height based on content
      const tempCanvas = createCanvas(width, 1000); // temporary canvas for measurements
      const tempCtx = tempCanvas.getContext('2d');
      const calculatedHeight = this.calculateRequiredHeight(
        tempCtx,
        product,
        width,
      );

      // Create the actual canvas with calculated height
      const canvas = createCanvas(width, calculatedHeight);
      const ctx = canvas.getContext('2d');

      // Set background color
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, calculatedHeight);

      // Draw prescription content
      this.drawSimplePrescription(
        ctx,
        request,
        product,
        width,
        calculatedHeight,
      );

      // Convert canvas to base64 string
      return canvas.toBuffer('image/png').toString('base64');
    } catch (error) {
      this.logger.error('Failed to generate prescription image', error);
      throw new Error(`Prescription image generation failed: ${error.message}`);
    }
  }

  private drawSimplePrescription(
    ctx: CanvasRenderingContext2D,
    request: PrescriptionRequest,
    product: PharmacyPrescriptionProduct,
    width: number,
    height: number,
  ) {
    const margin = 20;
    const fieldHeight = 30;
    let y = margin;

    // Draw border
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.strokeRect(0, 0, width, height);

    // Set font for all text
    ctx.fillStyle = '#000000';
    ctx.font = '16px Liberation Sans';
    ctx.textAlign = 'left';

    // To Red Rock Pharmacy (top center)
    ctx.font = 'bold 18px Liberation Sans';
    ctx.textAlign = 'center';
    ctx.fillText('To Red Rock Pharmacy', width / 2, y + 20);
    y += 40; // Add spacing after title

    // Prescribed Date (top right)
    const prescribedDate = request.prescriptionIssueDate
      ? format(
          new Date(request.prescriptionIssueDate + 'T00:00:00'),
          'MM/dd/yyyy',
        )
      : format(new Date(), 'MM/dd/yyyy');

    ctx.font = '16px Liberation Sans';
    ctx.textAlign = 'right';
    ctx.fillText(`Prescribed Date: ${prescribedDate}`, width - margin, y + 20);

    // Reset alignment and font
    ctx.textAlign = 'left';
    ctx.font = '16px Liberation Sans';
    y += fieldHeight + 15;

    // Patient Name
    ctx.fillText(
      `Patient Name: ${request.patient.firstName} ${request.patient.lastName}`,
      margin,
      y,
    );
    y += fieldHeight;

    // Date of Birth
    const dob = format(
      new Date(request.patient.dateOfBirth + 'T00:00:00'),
      'MM/dd/yyyy',
    );
    ctx.fillText(`Date of Birth: ${dob}`, margin, y);
    y += fieldHeight;

    // Patient Address
    const address = `${request.patient.address.street1}${request.patient.address.street2 ? ', ' + request.patient.address.street2 : ''}, ${request.patient.address.city}, ${request.patient.address.state} ${request.patient.address.zipCode}`;
    ctx.fillText(`Address: ${address}`, margin, y);
    y += fieldHeight + 10;

    // Horizontal line separator
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(margin, y);
    ctx.lineTo(width - margin, y);
    ctx.stroke();
    y += 20;

    // Drug Name
    ctx.fillText(`Drug: ${product.name || '<Drug Name>'}`, margin, y);
    y += fieldHeight;

    // Directions
    const directions = product.sig || 'Take as directed';
    // Split long directions into multiple lines if needed
    const maxWidth = width - margin * 2 - 20; // Additional padding for safety
    const directionLines = this.wrapText(ctx, directions, maxWidth);

    // First line with "Directions:" label
    if (directionLines.length > 0) {
      // Check if the first line fits with the label
      const labelWidth = ctx.measureText('Directions: ').width;
      const remainingWidth = maxWidth - labelWidth;
      const firstLineWords = directionLines[0].split(' ');
      let firstLineFits = '';
      let remainingFirstLine = '';

      // Try to fit as many words as possible on the first line with the label
      for (let i = 0; i < firstLineWords.length; i++) {
        const testLine =
          firstLineFits + (firstLineFits ? ' ' : '') + firstLineWords[i];
        if (ctx.measureText(testLine).width <= remainingWidth) {
          firstLineFits = testLine;
        } else {
          remainingFirstLine = firstLineWords.slice(i).join(' ');
          break;
        }
      }

      // Use consistent line height for all direction lines
      const lineHeight = 22; // Consistent line height for all lines

      // Draw the first line with label
      ctx.fillText(`Directions: ${firstLineFits}`, margin, y);
      y += lineHeight; // Use consistent line height

      // If there's remaining text from the first line, add it as a new line
      const continuationLines = [];
      if (remainingFirstLine) {
        continuationLines.push(remainingFirstLine);
      }

      // Add the rest of the lines
      for (let i = 1; i < directionLines.length; i++) {
        continuationLines.push(directionLines[i]);
      }

      // Re-wrap the continuation lines if needed
      const allContinuationText = continuationLines.join(' ');
      const rewrappedLines = allContinuationText
        ? this.wrapText(ctx, allContinuationText, maxWidth)
        : [];

      // Draw all continuation lines with proper indentation
      const indentSpaces = '   '; // Just 3 spaces for subtle indentation
      for (const line of rewrappedLines) {
        ctx.fillText(`${indentSpaces}${line}`, margin, y);
        y += lineHeight; // Use same line height as first line
      }
    }

    // Add some spacing after directions
    y += 10;

    // Horizontal line separator
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(margin, y);
    ctx.lineTo(width - margin, y);
    ctx.stroke();
    y += 20;

    // Quantity and Refills (on same line)
    const quantity = product.quantity || 1;
    const refills = '0';

    ctx.fillText(`Quantity: ${quantity}`, margin, y);
    ctx.fillText(`Refills: ${refills}`, width - 150, y);
    y += fieldHeight + 10;

    // Horizontal line separator
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(margin, y);
    ctx.lineTo(width - margin, y);
    ctx.stroke();
    y += 20;

    // Medical Reasoning
    ctx.font = 'bold 16px Liberation Sans';
    ctx.fillText('Medical Reasoning:', margin, y);
    y += fieldHeight;

    ctx.font = '14px Liberation Sans';
    const medicalReasoning =
      "MEDICALLY NECESSARY - patient can't tolerate rapid dose titration and experiences muscle loss; compounding with glycine required to minimize muscle wasting; pharmacy to compound - bill to WILLOW ship to patient";
    const medicalReasoningLines = this.wrapText(
      ctx,
      medicalReasoning,
      maxWidth,
    );
    for (const line of medicalReasoningLines) {
      ctx.fillText(line, margin, y);
      y += 20;
    }

    // Horizontal line separator
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(margin, y);
    ctx.lineTo(width - margin, y);
    ctx.stroke();
    y += 50;

    // Prescriber Name
    ctx.font = '16px Liberation Sans';
    ctx.fillText(
      `Prescriber Name: ${request.prescriber.firstName} ${request.prescriber.lastName}`,
      margin,
      y,
    );
    y += fieldHeight;

    // Prescriber NPI
    ctx.fillText(`Prescriber NPI: ${request.prescriber.npi}`, margin, y);
    y += fieldHeight;

    // Prescriber Address (from request data)
    let prescriberAddress = 'Wilmington, DE 19801'; // fallback
    if (request.prescriber.address) {
      const addr = request.prescriber.address;
      prescriberAddress = `${addr.city}, ${addr.state} ${addr.zipCode}`;
    }
    ctx.fillText(`Prescriber Address: ${prescriberAddress}`, margin, y);
    y += fieldHeight;

    // Prescriber Phone (from request data)
    const prescriberPhone = request.prescriber.phoneNumber || '(*************';
    ctx.fillText(`Prescriber Phone: ${prescriberPhone}`, margin, y);
    y += fieldHeight;

    y += 20; // Add spacing before signature

    // E-Signature (using custom Creattion font)
    try {
      ctx.font = '36px Creattion'; // Increased by 50% from 24px
    } catch {
      // Fallback to italic Times New Roman if custom font fails
      ctx.font = 'italic 36px Times New Roman';
    }
    ctx.fillText(
      `Dr. ${request.prescriber.firstName} ${request.prescriber.lastName}`,
      margin,
      y,
    );
    y += 40; // Increased spacing to accommodate larger signature

    // Prescriber Signature label
    ctx.font = '14px Liberation Sans';
    ctx.fillText('Prescriber Signature', margin, y);
    y += fieldHeight;

    // Date Signed (bottom right)
    const dateSigned = request.prescriptionIssueDate
      ? format(
          new Date(request.prescriptionIssueDate + 'T00:00:00'),
          'MM/dd/yyyy',
        )
      : format(new Date(), 'MM/dd/yyyy');

    ctx.textAlign = 'right';
    ctx.fillText(`Date Signed: ${dateSigned}`, width - margin, y);
  }

  private wrapText(
    ctx: CanvasRenderingContext2D,
    text: string,
    maxWidth: number,
  ): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine + (currentLine ? ' ' : '') + word;
      const metrics = ctx.measureText(testLine);

      if (metrics.width > maxWidth && currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine = testLine;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  private calculateRequiredHeight(
    ctx: CanvasRenderingContext2D,
    product: PharmacyPrescriptionProduct,
    width: number,
  ): number {
    const margin = 20;
    const fieldHeight = 30;
    let totalHeight = margin;

    // Set font for measurements
    ctx.font = '16px Liberation Sans';

    // Title
    totalHeight += 40; // Title + spacing

    // Prescribed Date
    totalHeight += fieldHeight + 15;

    // Patient info (name, DOB, address)
    totalHeight += fieldHeight * 3 + 10;

    // First separator
    totalHeight += 20;

    // Drug name
    totalHeight += fieldHeight;

    // Directions - calculate wrapped lines with improved handling
    const directions = product.sig || 'Take as directed';
    const maxWidth = width - margin * 2 - 20; // Match the drawing function
    const directionLines = this.wrapText(ctx, directions, maxWidth);

    // Account for the first line with "Directions:" label
    if (directionLines.length > 0) {
      const lineHeight = 22; // Consistent line height for all direction lines
      totalHeight += lineHeight; // First line

      // Calculate continuation lines
      const labelWidth = ctx.measureText('Directions: ').width;
      const remainingWidth = maxWidth - labelWidth;
      const firstLineWords = directionLines[0].split(' ');
      let firstLineFits = '';
      let remainingFirstLine = '';

      for (let i = 0; i < firstLineWords.length; i++) {
        const testLine =
          firstLineFits + (firstLineFits ? ' ' : '') + firstLineWords[i];
        if (ctx.measureText(testLine).width <= remainingWidth) {
          firstLineFits = testLine;
        } else {
          remainingFirstLine = firstLineWords.slice(i).join(' ');
          break;
        }
      }

      const continuationLines = [];
      if (remainingFirstLine) {
        continuationLines.push(remainingFirstLine);
      }

      for (let i = 1; i < directionLines.length; i++) {
        continuationLines.push(directionLines[i]);
      }

      if (continuationLines.length > 0) {
        const allContinuationText = continuationLines.join(' ');
        const rewrappedLines = this.wrapText(
          ctx,
          allContinuationText,
          maxWidth,
        );
        totalHeight += lineHeight * rewrappedLines.length;
      }
    }

    // Add spacing after directions
    totalHeight += 10;

    // Second separator
    totalHeight += 20;

    // Quantity and Refills
    totalHeight += fieldHeight + 10;

    // Third separator
    totalHeight += 20;

    // Medical Reasoning header
    totalHeight += fieldHeight;

    // Medical reasoning text
    ctx.font = '14px Liberation Sans';
    const medicalReasoning =
      "MEDICALLY NECESSARY - patient can't tolerate rapid dose titration and experiences muscle loss; compounding with glycine required to minimize muscle wasting; pharmacy to compound - bill to WILLOW ship to patient";
    const medicalReasoningLines = this.wrapText(
      ctx,
      medicalReasoning,
      maxWidth,
    );
    totalHeight += medicalReasoningLines.length * 20;

    // Fourth separator
    totalHeight += 50;

    // Prescriber info (name, NPI, address, phone) - all always present
    const prescriberFields = 4; // name, NPI, address, phone
    totalHeight += fieldHeight * prescriberFields + 20;

    // Signature
    totalHeight += 40; // Increased for larger signature

    // Signature label
    totalHeight += fieldHeight;

    // Date signed
    totalHeight += fieldHeight;

    // Bottom margin
    totalHeight += margin;

    return Math.max(totalHeight, 700); // Ensure minimum height
  }
}
