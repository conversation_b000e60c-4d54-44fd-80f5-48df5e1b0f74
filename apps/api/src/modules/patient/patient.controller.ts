import { HeaderAuthGuard } from '@modules/auth/guards/header-auth.guard';
import { PreventImpersonatedEditGuard } from '@modules/auth/guards/prevent-impersonated-edit.guard';
import { ShippingInfoDto } from '@modules/onboarding/dto/shipping-info.dto';
import { PatientSelectStateDto } from '@modules/patient/dto/patient-select-state.dto';
import { UpdateProfileDto } from '@modules/patient/dto/update-profile.dto';
import { WaitingListDto } from '@modules/patient/dto/waiting-list.dto';
import { PatientService } from '@modules/patient/patient.service';
import { PatientAddToWaitingListUseCase } from '@modules/patient/use-cases/patient-add-to-waiting-list-use.case';
import { PatientGetBillingAddressUseCase } from '@modules/patient/use-cases/patient-get-billing-address.use-case';
import { PatientGetPreSignedUrlUseCase } from '@modules/patient/use-cases/patient-get-pre-signed-url.use-case';
import { PatientGetShippingAddressUseCase } from '@modules/patient/use-cases/patient-get-shipping-address.use-case';
import { PatientGetStatusUseCase } from '@modules/patient/use-cases/patient-get-status.use-case';
import { PatientRestoreSubscriptionUseCase } from '@modules/patient/use-cases/patient-restore-subscription-use-case';
import { PatientSelectStateUseCase } from '@modules/patient/use-cases/patient-select-state.use-case';
import { PatientSetupPaymentIntentUseCase } from '@modules/patient/use-cases/patient-setup-payment-intent.use-case';
import { PatientSoftDeleteProfileUseCase } from '@modules/patient/use-cases/patient-softdelete-profile.use-case';
import { PatientUpdateBillingAddressUseCase } from '@modules/patient/use-cases/patient-update-billing-address.use-case';
import { PatientUpdatePhotoUseCase } from '@modules/patient/use-cases/patient-update-photo.use-case';
import { PatientUpdateProfileUseCase } from '@modules/patient/use-cases/patient-update-profile.use-case';
import { PatientUpdateShippingAddressUseCase } from '@modules/patient/use-cases/patient-update-shipping-address.use-case';
import { AddressMismatchError } from '@modules/shared/errors/address-mismatch.error';
import { ForbiddenError } from '@modules/shared/errors/forbidden.error';
import { PoBoxError } from '@modules/shared/errors/po-box.error';
import { GetStatesUseCase } from '@modules/shared/use-cases/states/get-states.use-case.service';
import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';

import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { roles } from '../auth/types/roles';
import { PrismaService } from '../prisma/prisma.service';
import { PatientSendReferralEmailsDto } from './dto/patient-send-referral-emails.dto';
import { PatientUnsubscribeDto } from './dto/patient-unsubscribe.dto';
import { GetPatientByIdUseCase } from './use-cases/get-patient-by-id.use-case';
import { PatientCancelSubscriptionUseCase } from './use-cases/patient-cancel-subscription.use-case';
import { PatientEligibleForCancelationDiscountUseCase } from './use-cases/patient-eligible-for-cancelation-discount.use-case';
import { PatientGetHsaReceiptUseCase } from './use-cases/patient-get-hsa-receipt.use-case';
import { PatientGetLetterOfMedicalNecessityUseCase } from './use-cases/patient-get-letter-of-medical-necesity.use-case';
import { PatientGetProfileInfoUseCase } from './use-cases/patient-get-profile-info.use-case';
import { PatientReferFriendUseCase } from './use-cases/patient-refer-friend.use-case';
import { PatientRequestCancelationDiscountUseCase } from './use-cases/patient-request-cancelation-discount.use-case';
import { PatientUpdatePhotosUseCase } from './use-cases/patient-update-photos.use-case';
import { PatientUpdateProfileByAdminUseCase } from './use-cases/patient-update-profile-by-admin.use-case';

@Controller('patient')
export class PatientController {
  constructor(
    private readonly prisma: PrismaService,
    private readonly patientService: PatientService,
    private readonly userGetStatesUseCase: GetStatesUseCase,
    private readonly patientGetStatusUseCase: PatientGetStatusUseCase,
    private readonly patientGetProfileInfoUseCase: PatientGetProfileInfoUseCase,
    private readonly patientAddToWaitingListUseCase: PatientAddToWaitingListUseCase,
    private readonly getPatientByIdUseCase: GetPatientByIdUseCase,
    private readonly patientSelectStateUseCase: PatientSelectStateUseCase,
    private readonly patientUpdatePhotoUseCase: PatientUpdatePhotoUseCase,
    private readonly patientUpdatePhotosUseCase: PatientUpdatePhotosUseCase,
    private readonly patientUpdateProfileUseCase: PatientUpdateProfileUseCase,
    private readonly patientGetBillingAddressUseCase: PatientGetBillingAddressUseCase,
    private readonly patientUpdateBillingAddressUseCase: PatientUpdateBillingAddressUseCase,
    private readonly patientUpdateShippingAddressUseCase: PatientUpdateShippingAddressUseCase,
    private readonly patientGetPreSignedUrlUseCase: PatientGetPreSignedUrlUseCase,
    private readonly patientGetShippingAddressUseCase: PatientGetShippingAddressUseCase,
    private readonly patientSetupPaymentIntentUseCase: PatientSetupPaymentIntentUseCase,
    private readonly patientSoftDeleteProfileUseCase: PatientSoftDeleteProfileUseCase,
    private readonly patientCancelSubscriptionUseCase: PatientCancelSubscriptionUseCase,
    private readonly patientEligibleForCancelationDiscountUseCase: PatientEligibleForCancelationDiscountUseCase,
    private readonly patientRequestCancelationDiscountUseCase: PatientRequestCancelationDiscountUseCase,
    private readonly patientRestoreSubscriptionUseCase: PatientRestoreSubscriptionUseCase,
    private readonly patientUpdateProfileByAdminUseCase: PatientUpdateProfileByAdminUseCase,
    private readonly patientReferFriendsUseCase: PatientReferFriendUseCase,
    private readonly patientGetHsaReceiptUseCase: PatientGetHsaReceiptUseCase,
    private readonly patientGetLetterOfMedicalNecessityUseCase: PatientGetLetterOfMedicalNecessityUseCase,
  ) {}

  @Get('states')
  async states() {
    try {
      return await this.userGetStatesUseCase.execute();
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('states')
  @HttpCode(200)
  async selectState(@Req() request: Request) {
    try {
      const body = request.body as PatientSelectStateDto;
      return await this.patientSelectStateUseCase.execute(
        body,
        request.headers,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('waitingList')
  async waitingList(
    @Req() request: Request,
    @Body() requestBody: WaitingListDto,
  ) {
    try {
      return await this.patientAddToWaitingListUseCase.execute(
        request,
        requestBody,
      );
    } catch (e) {
      if (e.code === 'P2002') {
        throw new BadRequestException('Email already exists');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('status')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles(roles.Patient)
  async status(@Req() request: Request) {
    try {
      const userId = request.user['userId'];
      if (!userId) throw new Error('Missing userId in request');
      return await this.patientGetStatusUseCase.execute(userId);
    } catch (e) {
      if (e instanceof ForbiddenError) {
        throw new ForbiddenException('Patient not onboarded');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('profile')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles(roles.Patient)
  async profile(@Req() request: Request) {
    try {
      const userId = request.user['userId'];
      return await this.patientGetProfileInfoUseCase.execute(userId);
    } catch (e) {
      if (e instanceof ForbiddenError) {
        throw new ForbiddenException('Patient not onboarded');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Post('profile')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles(roles.Patient)
  async updateProfile(
    @Req() request: Request,
    @Body() requestBody: UpdateProfileDto,
  ) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientUpdateProfileUseCase.execute(
        profile,
        requestBody,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Put(':patientId/admin-update-profile')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles(roles.Admin)
  async updateProfileByAdmin(
    @Req() request: Request,
    @Param('patientId') patientId: string,
    @Body() requestBody: UpdateProfileDto,
  ) {
    try {
      const userId = request.user['userId'];
      const admin = await this.prisma.admin.findUnique({
        where: { userId: userId },
      });
      return await this.patientUpdateProfileByAdminUseCase.execute(
        admin.id,
        patientId,
        requestBody,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('shippingAddress')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles(roles.Patient)
  async getShippingAddress(@Req() request: Request) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientGetShippingAddressUseCase.execute(profile);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('shippingAddress')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles(roles.Patient)
  async shippingAddress(
    @Req() request: Request,
    @Body() requestBody: ShippingInfoDto,
  ) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      await this.patientUpdateShippingAddressUseCase.execute(
        profile,
        requestBody,
      );
    } catch (e) {
      if (e instanceof AddressMismatchError) {
        throw new ConflictException({
          message: 'Address validation failed',
          error: 'Conflict',
          statusCode: 409,
          proposedAddress: e.address,
        });
      } else if (e instanceof PoBoxError) {
        throw new BadRequestException({
          message: e.message,
        });
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('billingAddress')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles(roles.Patient)
  async getBillingAddress(@Req() request: Request) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientGetBillingAddressUseCase.execute(profile);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('billingAddress')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles(roles.Patient)
  @HttpCode(200)
  async billingAddress(
    @Req() request: Request,
    @Body() requestBody: ShippingInfoDto,
  ) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientUpdateBillingAddressUseCase.execute(
        profile,
        requestBody,
      );
    } catch (e) {
      if (e instanceof AddressMismatchError) {
        throw new ConflictException({
          message: 'Address validation failed',
          error: 'Conflict',
          statusCode: 409,
          proposedAddress: e.address,
        });
      } else if (e instanceof PoBoxError) {
        throw new BadRequestException({
          message: e.message,
        });
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('get-upload-url/:type')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles(roles.Patient)
  async getUploadUrl(@Req() request: Request, @Param('type') type: string) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientGetPreSignedUrlUseCase.execute(profile, type);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('photos')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles(roles.Patient)
  @HttpCode(200)
  async photo(@Req() request: Request) {
    // @Body() requestBody: UploadPhotoDto
    try {
      // const { type, skip } = requestBody;
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientUpdatePhotosUseCase.execute(profile);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  // what can a patient do in their dashboard?
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles([roles.Doctor, roles.Patient])
  @Get(':patientId/user')
  async getPatientById(@Param('patientId') patientId: string) {
    try {
      return await this.getPatientByIdUseCase.execute(patientId);
    } catch (e) {
      return new BadRequestException(e.message);
    }
  }

  @Post('unsubscribe')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles(roles.Patient)
  @HttpCode(200)
  async unsubscribe(
    @Req() request: Request,
    @Body() requestBody: PatientUnsubscribeDto,
  ) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientCancelSubscriptionUseCase.execute({
        ...requestBody,
        patientId: profile.id,
        cancelledBy: {
          type: 'PATIENT',
          id: profile.id,
          userId,
        },
      });
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('elegible-for-cancelation-discount')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles(roles.Patient)
  @HttpCode(200)
  async elegibleForCancelationDiscount(@Req() request: Request) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientEligibleForCancelationDiscountUseCase.execute(
        profile,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('request-cancelation-discount')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles(roles.Patient)
  @HttpCode(200)
  async requestCancelationDiscount(@Req() request: Request) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientRequestCancelationDiscountUseCase.execute(
        profile,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('setup-payment-intent')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles([roles.Patient])
  async setupPaymentIntent(@Req() request: Request) {
    try {
      console.log('setupPaymentIntent');
      // get user data from cookie, cache or database
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientSetupPaymentIntentUseCase.execute(profile);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Delete(':patientEmail')
  @UseGuards(
    HeaderAuthGuard(
      'X-Soft-Delete-Patient',
      process.env.SOFT_DELETE_PATIENT_SECRET,
    ),
  )
  async deleteProfile(@Param('patientEmail') patientEmail: string) {
    try {
      await this.patientSoftDeleteProfileUseCase.execute(patientEmail);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('restore-subscription')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles([roles.Patient])
  async restoreSubscription(@Req() request: Request) {
    try {
      const userId = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientRestoreSubscriptionUseCase.execute({
        patientId: profile.id,
        restoredBy: {
          type: 'PATIENT',
          userId,
          id: profile.id,
        },
      });
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('send-referral-emails')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles([roles.Patient])
  async sendReferralEmails(
    @Req() request: Request,
    @Body() requestBody: PatientSendReferralEmailsDto,
  ) {
    try {
      const userId: string = request.user['userId'];
      const profile = await this.patientService.getPatientData(userId);
      return await this.patientReferFriendsUseCase.execute(
        profile,
        requestBody.email,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('insurance/hsa-receipt/:year')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles([roles.Patient])
  async getHsaReceipt(@Req() request: Request, @Param('year') year: string) {
    const userId: string = request.user['userId'];
    return this.patientGetHsaReceiptUseCase.execute(userId, year);
  }

  @Get('insurance/letter-of-medical-necessity')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles([roles.Patient])
  async getLetterOfMedicalNecessity(@Req() request: Request) {
    const patientUserId: string = request.user['userId'];
    return await this.patientGetLetterOfMedicalNecessityUseCase.execute(
      patientUserId,
    );
  }
}
