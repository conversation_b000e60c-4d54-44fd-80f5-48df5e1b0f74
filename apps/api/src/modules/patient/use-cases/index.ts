import { GetPatientByIdUseCase } from '@modules/patient/use-cases/get-patient-by-id.use-case';
import { PatientAddToWaitingListUseCase } from '@modules/patient/use-cases/patient-add-to-waiting-list-use.case';
import { PatientCancelSubscriptionUseCase } from '@modules/patient/use-cases/patient-cancel-subscription.use-case';
import { PatientEligibleForCancelationDiscountUseCase } from '@modules/patient/use-cases/patient-eligible-for-cancelation-discount.use-case';
import { PatientGetBillingAddressUseCase } from '@modules/patient/use-cases/patient-get-billing-address.use-case';
import { PatientGetHsaReceiptUseCase } from '@modules/patient/use-cases/patient-get-hsa-receipt.use-case';
import { PatientGetPreSignedUrlUseCase } from '@modules/patient/use-cases/patient-get-pre-signed-url.use-case';
import { PatientGetProfileInfoUseCase } from '@modules/patient/use-cases/patient-get-profile-info.use-case';
import { PatientGetShippingAddressUseCase } from '@modules/patient/use-cases/patient-get-shipping-address.use-case';
import { PatientGetStatusUseCase } from '@modules/patient/use-cases/patient-get-status.use-case';
import { PatientLeadUseCase } from '@modules/patient/use-cases/patient-lead.use-case';
import { PatientReferFriendUseCase } from '@modules/patient/use-cases/patient-refer-friend.use-case';
import { PatientRefreshTokenUseCase } from '@modules/patient/use-cases/patient-refresh-token-use.case';
import { PatientRequestCancelationDiscountUseCase } from '@modules/patient/use-cases/patient-request-cancelation-discount.use-case';
import { PatientRestoreSubscriptionUseCase } from '@modules/patient/use-cases/patient-restore-subscription-use-case';
import { PatientSelectStateUseCase } from '@modules/patient/use-cases/patient-select-state.use-case';
import { PatientSetupPaymentIntentUseCase } from '@modules/patient/use-cases/patient-setup-payment-intent.use-case';
import { PatientSignInUseCase } from '@modules/patient/use-cases/patient-sign-in-use.case';
import { PatientSignUpUseCase } from '@modules/patient/use-cases/patient-sign-up.use-case';
import { PatientSoftDeleteProfileUseCase } from '@modules/patient/use-cases/patient-softdelete-profile.use-case';
import { PatientUpdateBillingAddressUseCase } from '@modules/patient/use-cases/patient-update-billing-address.use-case';
import { PatientUpdatePhotoUseCase } from '@modules/patient/use-cases/patient-update-photo.use-case';
import { PatientUpdatePhotosUseCase } from '@modules/patient/use-cases/patient-update-photos.use-case';
import { PatientUpdateProfileByAdminUseCase } from '@modules/patient/use-cases/patient-update-profile-by-admin.use-case';
import { PatientUpdateProfileUseCase } from '@modules/patient/use-cases/patient-update-profile.use-case';
import { PatientUpdateShippingAddressUseCase } from '@modules/patient/use-cases/patient-update-shipping-address.use-case';

import { PatientGetLetterOfMedicalNecessityUseCase } from './patient-get-letter-of-medical-necesity.use-case';

export const PatientUseCases = [
  PatientGetLetterOfMedicalNecessityUseCase,
  GetPatientByIdUseCase,
  PatientGetProfileInfoUseCase,
  PatientCancelSubscriptionUseCase,
  PatientEligibleForCancelationDiscountUseCase,
  PatientLeadUseCase,
  PatientRefreshTokenUseCase,
  PatientRequestCancelationDiscountUseCase,
  PatientRestoreSubscriptionUseCase,
  PatientSignInUseCase,
  PatientSignUpUseCase,
  PatientAddToWaitingListUseCase,
  PatientGetStatusUseCase,
  PatientSelectStateUseCase,
  PatientUpdatePhotoUseCase,
  PatientUpdateShippingAddressUseCase,
  PatientGetBillingAddressUseCase,
  PatientUpdateBillingAddressUseCase,
  PatientGetPreSignedUrlUseCase,
  PatientUpdateProfileUseCase,
  PatientGetShippingAddressUseCase,
  PatientSetupPaymentIntentUseCase,
  PatientSoftDeleteProfileUseCase,
  PatientUpdateProfileByAdminUseCase,
  PatientReferFriendUseCase,
  PatientUpdatePhotosUseCase,
  PatientGetHsaReceiptUseCase,
];
