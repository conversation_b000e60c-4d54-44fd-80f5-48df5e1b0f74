import { Module } from '@nestjs/common';

import { AppCacheModule } from '../cache/cache.module';
import { PrismaModule } from '../prisma/prisma.module';
import { StateController } from './state.controller';
import { StateService } from './state.service';

@Module({
  imports: [PrismaModule, AppCacheModule],
  controllers: [StateController],
  providers: [StateService],
  exports: [StateService],
})
export class StateModule {}
