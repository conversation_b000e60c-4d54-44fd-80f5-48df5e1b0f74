import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { CacheService } from '../cache/cache.service';
import { PrismaService } from '../prisma/prisma.service';
import { UpdateStateDto } from './dto/update-state.dto';

@Injectable()
export class StateService {
  private readonly CACHE_FRESH = 60; // 1 hour
  private readonly CACHE_STALE = 86400; // 1 day

  constructor(
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * Get patient count for a specific state with caching
   * Uses flexible cache with stale-while-revalidate pattern
   */
  private async getPatientCountForState(stateId: string): Promise<number> {
    const cacheKey = `state:patient-count:${stateId}`;

    return this.cacheService.flexible<number>(
      cacheKey,
      [this.CACHE_FRESH, this.CACHE_STALE],
      async () => {
        // Count patients in this state, excluding deleted users
        return this.prisma.patient.count({
          where: {
            stateId,
            user: { deletedAt: null },
          },
        });
      },
    );
  }

  async findAll(options?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    direction?: 'asc' | 'desc';
    includePatientCounts?: boolean;
  }) {
    const {
      page = 1,
      limit = 20,
      search = '',
      sortBy = 'name',
      direction = 'asc',
      includePatientCounts = false,
    } = options || {};

    // Create the where clause for filtering
    let where: Prisma.StateWhereInput = {};

    if (search) {
      where = {
        OR: [
          { name: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { code: { contains: search, mode: Prisma.QueryMode.insensitive } },
        ],
      };
    }

    // Special handling for patient count sorting
    if (sortBy === 'patientCount' && includePatientCounts) {
      // When sorting by patient count, we need to fetch all states first
      // This is acceptable since there are only 50 states
      const allStates = await this.prisma.state.findMany({
        where,
        select: { name: true, id: true, code: true, enabled: true },
        orderBy: { name: 'asc' }, // Default order while fetching all
      });

      // Add patient counts to all states
      const allStatesWithCounts = await Promise.all(
        allStates.map(async (state) => {
          const patientCount = await this.getPatientCountForState(state.id);
          return {
            ...state,
            patientCount,
          };
        }),
      );

      // Sort by patient count
      allStatesWithCounts.sort((a, b) => {
        const aCount = a.patientCount || 0;
        const bCount = b.patientCount || 0;
        return direction === 'asc' ? aCount - bCount : bCount - aCount;
      });

      // Manual pagination
      const paginatedStates = allStatesWithCounts.slice(
        (page - 1) * limit,
        page * limit,
      );

      return {
        states: paginatedStates,
        pagination: {
          total: allStatesWithCounts.length,
          page,
          limit,
          totalPages: Math.ceil(allStatesWithCounts.length / limit),
        },
      };
    }

    // Normal flow for other sort fields
    const total = await this.prisma.state.count({ where });

    const orderBy = {
      [sortBy === 'code' || sortBy === 'enabled' ? sortBy : 'name']: direction,
    };

    // Fetch paginated states
    const states = await this.prisma.state.findMany({
      where,
      select: { name: true, id: true, code: true, enabled: true },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });

    // If patient counts are requested (but not sorting by them), add counts
    let statesWithCounts: Array<
      (typeof states)[0] & { patientCount?: number }
    > = states;
    if (includePatientCounts) {
      statesWithCounts = await Promise.all(
        states.map(async (state) => {
          const patientCount = await this.getPatientCountForState(state.id);
          return {
            ...state,
            patientCount,
          };
        }),
      );
    }

    return {
      states: statesWithCounts,
      pagination: { total, page, limit, totalPages: Math.ceil(total / limit) },
    };
  }

  async findOne(id: string) {
    return this.prisma.state.findUnique({
      where: { id },
      select: { id: true, name: true, code: true, enabled: true },
    });
  }

  async update(id: string, data: UpdateStateDto) {
    return this.prisma.state.update({
      where: { id },
      data: { enabled: data.enabled },
      select: { id: true, name: true, code: true, enabled: true },
    });
  }

  /*
  Get the all the states with their available doctors for every state and the "priority pharmacy"
  the "priority pharmacy" is the patient pharmacy if available in the state, otherwise the  pharmacy with the higher priority in the state
  */
  async findAllWithPharmaciesForPatient(patientId: string) {
    const patient = await this.prisma.patient.findFirst({
      select: { doctorId: true, pharmacyId: true, stateId: true },
      where: { id: patientId },
    });

    const patientPharmacyId = patient?.pharmacyId;
    const patientStateId = patient?.stateId;
    const patientDoctorId = patient?.doctorId;

    const states = await this.prisma.state.findMany({
      where: { NOT: { id: patientStateId }, enabled: true },
      include: {
        doctorStates: {
          where: { doctor: { user: { deletedAt: null } } },
          include: { doctor: { include: { user: true } } },
        },
        pharmacyOnState: { include: { pharmacy: true } },
      },
      orderBy: { name: 'asc' },
    });

    return states.map((state) => {
      const currentDoctorAvailable = patientDoctorId
        ? state.doctorStates.some((ds) => ds.doctorId === patientDoctorId)
        : false;

      // Sort eligible doctors by role: superDoctors first, regular doctors later
      state.doctorStates.sort((a, b) => {
        if (
          a.doctor.role === 'superDoctor' &&
          b.doctor.role !== 'superDoctor'
        ) {
          return -1;
        }
        if (
          a.doctor.role !== 'superDoctor' &&
          b.doctor.role === 'superDoctor'
        ) {
          return 1;
        }
        return 0;
      });

      state.pharmacyOnState.sort(
        (a, b) => b.pharmacy.regularPriority - a.pharmacy.regularPriority,
      );

      const patientPharmacy =
        patientPharmacyId &&
        state.pharmacyOnState.find((ph) => ph.pharmacyId === patientPharmacyId)
          ?.pharmacy;

      const currentPharmacyAvailable = !!patientPharmacy;
      const priorityPharmacy =
        patientPharmacy || state.pharmacyOnState[0].pharmacy;

      return {
        id: state.id,
        code: state.code,
        name: state.name,
        enabled: state.enabled,
        doctors: state.doctorStates,
        newPharmacy: priorityPharmacy,
        currentDoctorAvailable,
        currentPharmacyAvailable,
      };
    });
  }

  /*
  Get the state with the available doctors in that state and the "priority pharmacy"
  the "priority pharmacy" is the patient pharmacy if available in the state, otherwise the  pharmacy  with the higher priority in the state
  */
  async findOneWithPharmaciesForPatient(patientId: string, stateCode: string) {
    const patientPharmacyId = (
      await this.prisma.patient.findFirst({
        select: { pharmacyId: true },
        where: {
          id: patientId,
        },
      })
    )?.pharmacyId;

    const state = await this.prisma.state.findFirst({
      where: {
        code: stateCode,
      },
      include: {
        pharmacyOnState: {
          include: {
            pharmacy: true,
          },
        },
      },
    });

    if (!state) return null;

    state.pharmacyOnState.sort(
      (a, b) => b.pharmacy.regularPriority - a.pharmacy.regularPriority,
    );

    //find the patient pharmacy in the state, if found use it
    const patientPharmacy =
      patientPharmacyId &&
      state.pharmacyOnState.find((ph) => ph.pharmacyId === patientPharmacyId)
        ?.pharmacy;

    const priorityPharmacy =
      patientPharmacy || state.pharmacyOnState[0].pharmacy;

    return {
      id: state.id,
      code: state.code,
      name: state.name,
      enabled: state.enabled,
      newPharmacy: priorityPharmacy,
      pharmacyOnState: state?.pharmacyOnState || [],
    };
  }
}
