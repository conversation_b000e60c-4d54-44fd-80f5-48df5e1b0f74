import { RequireCapabilities } from '@modules/auth/decorators/require-capabilities.decorator';
import { SuperAdminGuard } from '@modules/auth/decorators/super-admin.decorator';
import { CapabilityGuard } from '@modules/auth/guards/capability.guard';
import { roles } from '@modules/auth/types/roles';
import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

import { Capability } from '@willow/auth';

import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { UpdateStateDto } from './dto/update-state.dto';
import { StateService } from './state.service';

@Controller('state')
@UseGuards(AuthGuard('jwt'), RolesGuard)
@Roles(roles.Admin)
export class StateController {
  constructor(private readonly stateService: StateService) {}

  @Get()
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('direction') direction?: 'asc' | 'desc',
    @Query('includePatientCounts') includePatientCounts?: string,
  ) {
    return this.stateService.findAll({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      search,
      sortBy,
      direction,
      includePatientCounts: includePatientCounts === 'true',
    });
  }

  @Get(':id')
  @UseGuards(CapabilityGuard)
  @RequireCapabilities(Capability.VIEW_STATES)
  findOne(@Param('id') id: string) {
    return this.stateService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(CapabilityGuard)
  @RequireCapabilities(Capability.EDIT_STATES)
  update(@Param('id') id: string, @Body() updateStateDto: UpdateStateDto) {
    return this.stateService.update(id, updateStateDto);
  }
}
