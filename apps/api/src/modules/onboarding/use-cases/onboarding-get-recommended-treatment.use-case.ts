import { PatientOnboardingProfile } from '@adapters/persistence/database/patient.persistence';
import {
  OnboardingActor,
  OnboardingSnapshot,
  OnboardingStateService,
} from '@modules/onboarding/onboarding-state.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class OnboardingGetRecommendedTreatmentUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly onboardingService: OnboardingStateService,
  ) {}

  async execute(profile: PatientOnboardingProfile, formOrCategoryId: string) {
    const onboarding = (await this.onboardingService.getCurrentOnboardingActor(
      profile.questionnaire.version,
      profile.onboardingState as unknown as OnboardingSnapshot,
    )) as OnboardingActor;
    const snapshot = onboarding.getSnapshot();

    // Determine if we have a legacy form value or a category ID
    const legacyForms = ['oral', 'injectable', 'tablet'];
    const isLegacyForm = legacyForms.includes(formOrCategoryId);

    let productCategoryId: string;
    let categoryName: string = '';

    if (isLegacyForm) {
      // Legacy form value - find the corresponding product category
      const patientState = profile.state;

      // Find pharmacies that serve this state
      const pharmacyStateRecords =
        await this.prismaService.pharmacyOnState.findMany({
          where: { state: patientState, pharmacy: { enabled: true } },
          select: { pharmacyId: true },
        });

      const eligiblePharmacyIds = pharmacyStateRecords.map(
        (record) => record.pharmacyId,
      );

      // Get product categories with products from eligible pharmacies
      const productCategories =
        await this.prismaService.productCategory.findMany({
          where: {
            enabled: true,
            form: formOrCategoryId,
            products: {
              some: {
                product: {
                  active: true,
                  isCore: true,
                  isAvailableInOnboarding: true,
                  pharmacyId: { in: eligiblePharmacyIds },
                },
              },
            },
          },
          orderBy: { order: 'asc' },
        });

      if (productCategories.length === 0) {
        throw new Error(
          `No product category found for form: ${formOrCategoryId}`,
        );
      }

      // Use the first matching category (ordered by order field)
      productCategoryId = productCategories[0].id;
      categoryName = productCategories[0].name;
    } else {
      // Assume it's a category ID (UUID)
      const productCategory =
        await this.prismaService.productCategory.findUnique({
          where: { id: formOrCategoryId },
          select: { id: true, form: true, name: true },
        });

      if (!productCategory) {
        throw new Error(`Invalid product category ID: ${formOrCategoryId}`);
      }

      productCategoryId = productCategory.id;
      categoryName = productCategory.name;
    }

    const pharmacyId: string | { in: string[] } = snapshot.context.pharmacyId;

    const productsData = await this.prismaService.product.findMany({
      where: {
        active: true,
        isAvailableInOnboarding: true,
        pharmacyId: pharmacyId,
        productCategories: {
          some: {
            productCategoryId: productCategoryId,
          },
        },
        isCore: true,
      },
      include: { defaultPrice: true },
    });

    const products = productsData
      .filter((p) => p.onboardingLabel)
      .map((p) => {
        return {
          id: p.id,
          price_id: p.defaultPriceId,
          name: p.onboardingLabel || p.name,
          description: p.description,
          product: p.label?.toLowerCase(),
          image: p.image,
          price: p.defaultPrice.unit_amount / 100,
          type: p.type,
          tags: p.tags?.split(',') || [],
          supplyLength: parseInt(p.supplyLength || '0'),
          notice: p.notice,
          order: p.order,
          weightLossMultiplier: p.weightLossMultiplier || 0.85,
          customCard: p.customCard,
        };
      })
      .sort((a, b) => {
        if (a.type === 'core' && b.type !== 'core') return -1;
        if (a.type !== 'core' && b.type === 'core') return 1;
        return a.order - b.order;
      });

    // Also include non-core products (add-ons)
    const nonCoreProducts = await this.prismaService.product.findMany({
      where: {
        active: true,
        isAvailableInOnboarding: true,
        pharmacyId: pharmacyId,
        isCore: false,
      },
      include: { defaultPrice: true },
    });

    const nonCoreProductsMapped = nonCoreProducts
      .filter((p) => p.onboardingLabel)
      .map((p) => {
        return {
          id: p.id,
          price_id: p.defaultPriceId,
          name: p.onboardingLabel || p.name,
          description: p.description,
          product: p.label?.toLowerCase(),
          image: p.image,
          price: p.defaultPrice.unit_amount / 100,
          type: p.type,
          tags: p.tags?.split(',') || [],
          supplyLength: p.supplyLength || '0',
          notice: p.notice,
          order: p.order,
          weightLossMultiplier: 0.85,
          customCard: p.customCard,
        };
      });

    // Combine core and non-core products
    const allProducts = [...products, ...nonCoreProductsMapped].sort((a, b) => {
      if (a.type === 'core' && b.type !== 'core') return -1;
      if (a.type !== 'core' && b.type === 'core') return 1;
      return a.order - b.order;
    });

    return {
      products: allProducts,
      usesGLP1: snapshot.context.questionnaire['usingGLP1'] === 'yes',
      is90Days: categoryName.toLowerCase() === '90 day program',
    };
  }
}
