import type { Product, ProductCategory, ProductPrice } from '@prisma/client';
import { PatientOnboardingProfile } from '@adapters/persistence/database/patient.persistence';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

type TreatmentResponse = {
  tags: string[];
  image: string;
  id: string;
  name: string;
  basePrice: number;
  shortDescription: string;
  description: string;
  form?: string | null;
  customCard?: string | null;
};

type ProductWithDetails = Product & {
  defaultPrice: ProductPrice;
  productCategories: {
    productCategory: ProductCategory;
  }[];
};

@Injectable()
export class OnboardingGetTreatmentTypeUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(
    profile: PatientOnboardingProfile,
  ): Promise<TreatmentResponse[]> {
    const patientState = profile.state;

    // First, fetch all enabled product categories
    const productCategories = await this.prismaService.productCategory.findMany(
      {
        where: { enabled: true },
        orderBy: { order: 'asc' },
      },
    );

    if (productCategories.length === 0) {
      console.error('No enabled product categories found');
      return [];
    }

    // Find the pharmacy IDs that serve this state
    const pharmacyStateRecords =
      await this.prismaService.pharmacyOnState.findMany({
        where: { state: patientState, pharmacy: { enabled: true } },
        select: { pharmacyId: true },
      });

    const eligiblePharmacyIds = pharmacyStateRecords.map(
      (record) => record.pharmacyId,
    );

    if (eligiblePharmacyIds.length === 0) {
      console.error(`No pharmacies found that serve state: ${patientState}`);
      return [];
    }

    // Get all enabled pharmacies that serve the patient's state with their products
    const pharmacies = await this.prismaService.pharmacy.findMany({
      where: { enabled: true, id: { in: eligiblePharmacyIds } },
      orderBy: { regularPriority: 'desc' },
      include: {
        Product: {
          where: { active: true, isCore: true, isAvailableInOnboarding: true },
          include: {
            defaultPrice: true,
            productCategories: {
              include: {
                productCategory: true,
              },
            },
          },
          orderBy: { defaultPrice: { unit_amount: 'asc' } },
        },
      },
    });

    if (pharmacies.length === 0) {
      console.error(
        `No enabled pharmacies found that serve state: ${patientState}`,
      );
      return [];
    }

    // Collect all products from all pharmacies
    const allProducts: ProductWithDetails[] = [];
    for (const pharmacy of pharmacies) {
      for (const product of pharmacy.Product || []) {
        if (
          product.productCategories &&
          product.productCategories.length > 0 &&
          product.defaultPrice
        ) {
          allProducts.push(product as ProductWithDetails);
        } else {
          console.warn(
            `Product ${product.id} (${product.name}) missing category or price`,
          );
        }
      }
    }

    // Group products by category ID
    const productsByCategoryId = new Map<string, ProductWithDetails[]>();
    for (const product of allProducts) {
      // A product can belong to multiple categories
      for (const pc of product.productCategories) {
        const categoryId = pc.productCategory.id;
        if (!productsByCategoryId.has(categoryId)) {
          productsByCategoryId.set(categoryId, []);
        }
        productsByCategoryId.get(categoryId)!.push(product);
      }
    }

    // Build response for categories that have products
    const result: TreatmentResponse[] = [];

    for (const category of productCategories) {
      const categoryProducts = productsByCategoryId.get(category.id);

      // Only include categories that have at least one product
      if (categoryProducts && categoryProducts.length > 0) {
        // Find the product with the lowest price
        const lowestPriceProduct = categoryProducts.reduce((min, product) =>
          product.defaultPrice.unit_amount < min.defaultPrice.unit_amount
            ? product
            : min,
        );

        // Parse tags from tags field if available
        const tags: string[] = category.tags
          ? category.tags.split(',').map((tag) => tag.trim())
          : [];

        result.push({
          tags,
          image: category.image || '',
          id: category.id, // Use category ID instead of form
          name: category.name,
          basePrice: lowestPriceProduct.defaultPrice.unit_amount / 100,
          shortDescription:
            category.shortDescription || category.description || '',
          description: category.description || '',
          form: category.form,
          customCard: category.customCard,
        });
      }
    }

    // Results are already sorted by category order from the initial query
    return result;
  }
}
