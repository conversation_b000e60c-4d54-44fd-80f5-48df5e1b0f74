import {
  calcRoundedDiscountValue,
  calcRoundedTotalDisountedPrice,
  calcTreatmentTotalPrice,
} from '@/accounting.helpers';
import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { OnboardingStateService } from '@modules/onboarding/onboarding-state.service';
import { StripeService } from '@modules/stripe/service/stripe.service';
import { Injectable } from '@nestjs/common';
import Big from 'big.js';
import { AnyMachineSnapshot } from 'xstate';

import { ShippingInfoDto } from '../dto/shipping-info.dto';
import { OnboardingAttachedDiscountUseCase } from './onboarding-attached-discount.use-case';

@Injectable()
export class OnboardingSetupPaymentIntentUseCase {
  constructor(
    private readonly stripeService: StripeService,
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPersistence: PatientPersistence,
    private readonly onboardingAttachedDiscountUseCase: OnboardingAttachedDiscountUseCase,
  ) {}

  async execute(profile: PatientOnboardingProfile) {
    // check current onboarding state
    const onboarding = await this.onboardingService.getCurrentOnboardingActor(
      profile.questionnaire.version,
      profile.onboardingState as unknown as AnyMachineSnapshot,
    );
    const snapshot = onboarding.getSnapshot();
    if (snapshot.value !== 'payment') {
      throw new Error('Invalid onboarding state');
    }

    // create stripe customer if not exists
    let stripeCustomerId = profile.stripeCustomerId;
    const shippingAddresses = snapshot.context.shippingInfo as ShippingInfoDto;
    if (!stripeCustomerId) {
      const customer = await this.stripeService.createCustomer(
        profile.userId,
        profile.id,
        profile.user.email,
        `${profile.user.firstName} ${profile.user.lastName}`,
        profile.user.phone,
        {
          line1: shippingAddresses.address1,
          city: shippingAddresses.city,
          postal_code: shippingAddresses.zip,
          state: shippingAddresses.state,
        },
        {
          name: `${profile.user.firstName} ${profile.user.lastName}`,
          address: {
            line1: shippingAddresses.address1,
            city: shippingAddresses.city,
            postal_code: shippingAddresses.zip,
            state: shippingAddresses.state,
          },
        },
      );
      await this.patientPersistence.update(profile.id, {
        stripeCustomerId: customer.id,
      });
      stripeCustomerId = customer.id;
    }

    // get client secret if persisted in the context
    let clientSecret: string | null = snapshot.context.paymentIntent;

    if (clientSecret) {
      //check if client secret is still valid
      const valid =
        await this.stripeService.checkClientSecretValidity(clientSecret);
      // @todo remove old payment setup intent from DB
      clientSecret = valid ? clientSecret : null;
    }

    if (!clientSecret) {
      // create payment setup intent
      const setup =
        await this.stripeService.createPaymentSetupIntent(stripeCustomerId);

      // get the secret and store it in the onboarding state context
      clientSecret = setup.client_secret;
      onboarding.send({ type: 'update', value: clientSecret });
      await this.patientPersistence.updateOnboarding(
        profile.id,
        onboarding.getSnapshot(),
      );
    }

    const products = snapshot.context.products ?? [];
    const totalPrice = calcTreatmentTotalPrice(products);
    const discount = await this.onboardingAttachedDiscountUseCase.execute({
      userId: profile.userId,
    });
    const discountPrice = discount
      ? calcRoundedDiscountValue(totalPrice, discount.value, discount.valueType)
      : new Big(0);
    const totalDiscountedPrice = calcRoundedTotalDisountedPrice(
      totalPrice,
      discountPrice,
    );

    return {
      stripe: {
        clientSecret,
      },
      discount,
      totalPriceBeforeDiscount: totalPrice.toFixed(2),
      discountPrice: discountPrice.toFixed(2),
      totalDiscountedPrice: totalDiscountedPrice.toFixed(2),
      // discounts will be applied in stripe
      chargePrice: totalPrice.toFixed(2),
    };
  }
}
