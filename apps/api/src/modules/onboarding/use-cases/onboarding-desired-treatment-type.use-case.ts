import { AuditService } from '@/modules/audit-log/audit-log.service';
import { UtmService } from '@/modules/shared/services/utm.service';
import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import {
  OnboardingActor,
  OnboardingSnapshot,
  OnboardingStateService,
} from '@modules/onboarding/onboarding-state.service';
import { PharmacyService } from '@modules/pharmacy/pharmacy.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { segmentTrackEvents } from '@modules/shared/events';
import { SegmentTrack } from '@modules/shared/types/events';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { OnboardingEventEmitterService } from './onboarding-event-emitter.service';

@Injectable()
export class OnboardingDesiredTreatmentTypeUseCase {
  constructor(
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPersistence: PatientPersistence,
    private readonly onboardingEventEmitter: OnboardingEventEmitterService,
    private readonly eventEmitter: EventEmitter2,
    private readonly utmService: UtmService,
    private readonly auditService: AuditService,
    private readonly pharmacyService: PharmacyService,
    private readonly prismaService: PrismaService,
  ) {}

  async execute(profile: PatientOnboardingProfile, categoryId: string) {
    if (!categoryId) {
      throw new Error('Category ID is required');
    }

    // Fetch the product category to get its form
    const productCategory = await this.prismaService.productCategory.findUnique(
      {
        where: { id: categoryId },
        select: { id: true, form: true, name: true },
      },
    );

    if (!productCategory) {
      throw new Error('Invalid product category');
    }

    const onboarding = (await this.onboardingService.getCurrentOnboardingActor(
      profile.questionnaire.version,
      profile.onboardingState as unknown as OnboardingSnapshot,
    )) as OnboardingActor;

    //validate it's on selectTreatment state
    const state = onboarding.getSnapshot();
    if (state.value !== 'selectTreatmentType') {
      throw new Error('Invalid onboarding state');
    }

    // Validate pharmacy and update state if needed
    await this.validateAndSetPharmacy(
      profile.id,
      profile.stateId,
      categoryId,
      onboarding,
    );

    const result = this.onboardingService.performTransition(
      onboarding,
      'next',
      { productType: categoryId }, // TODO: Update state machine to use productCategoryId
    );

    const snapshot = result.getSnapshot();

    // persist new state
    await this.patientPersistence.updateOnboarding(profile.id, snapshot);

    void this.onboardingEventEmitter.execute(profile, snapshot);

    const campaignData = await this.utmService.getCampaignDataFromCache(
      profile.id,
    );

    // Use the form from the product category for audit log
    void this.auditService.append({
      patientId: profile.id,
      action: 'ONBOARDING_TREATMENT_FORM_SELECTED',
      actorType: 'PATIENT',
      actorId: profile.id,
      resourceType: 'PATIENT',
      resourceId: profile.id,
      details: {
        selectedForm:
          (productCategory.form as 'oral' | 'injectable' | 'tablet') ||
          'injectable',
      },
    });

    const treatmentTypeChosenEvent: SegmentTrack = {
      event: segmentTrackEvents.treatmentTypeChosen.name,
      userId: profile.id,
      context: {
        ...campaignData,
      },
      properties: {
        type: productCategory.form, // Keep as 'type' for analytics backward compatibility
        categoryId: categoryId,
        categoryName: productCategory.name,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.treatmentTypeChosen.event,
      treatmentTypeChosenEvent,
    );

    return this.onboardingService.getCurrentOnboardingState(
      profile.questionnaire.version,
      snapshot,
    );
  }

  private async validateAndSetPharmacy(
    patientId: string,
    stateId: string,
    categoryId: string,
    onboarding: OnboardingActor,
  ) {
    const snapshot = onboarding.getSnapshot();

    // Pass the responsibility to the pharmacy service to resolve the pharmacy
    // The service will handle fetching the patient, calculating the proper pharmacy,
    // and persisting the pharmacy ID to the patient record
    const selectedPharmacy = await this.pharmacyService.resolvePharmacyPriority(
      patientId,
      stateId,
      categoryId,
      snapshot,
    );

    if (!selectedPharmacy) {
      throw new Error(
        `No pharmacy available for product category: ${categoryId}`,
      );
    }

    // Update onboarding context if needed
    if (snapshot.context.pharmacyId !== selectedPharmacy.id) {
      snapshot.context.pharmacyId = selectedPharmacy.id;
      await this.patientPersistence.updateOnboarding(patientId, snapshot);
    }
  }
}
