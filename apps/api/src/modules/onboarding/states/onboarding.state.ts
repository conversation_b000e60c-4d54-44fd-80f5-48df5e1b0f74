import { ShippingInfoDto } from '@modules/onboarding/dto/shipping-info.dto';
import { DesiredTreatment } from '@modules/onboarding/use-cases/onboarding-desired-treatment.use-case';
import * as dayjs from 'dayjs';
import { assign, setup } from 'xstate';

export const onboardingMachine = setup({
  types: {
    context: {} as {
      rejected?: boolean;
      questionnaireCompleted: boolean;
      rejectedReason?: string;
      questionnaire: {
        birthDate: string;
        gender: 'male' | 'female';
      };
      pharmacyId?: string;
      productType?: string;
      products?: DesiredTreatment[];
      'id-photo'?: string;
      'face-photo'?: string;
      shippingInfo?: ShippingInfoDto;
      paymentIntent?: string;
    },
  },
  guards: {
    isUnderAge: function ({ event }) {
      const birthDate = dayjs(event.value.birthDate);
      const eighteenYearsAgo = dayjs().subtract(18, 'year');

      return birthDate.isAfter(eighteenYearsAgo);
    },

    isOverAge: function ({ event }) {
      const birthDate = dayjs(event.value.birthDate);
      const seventyFiveYearsAgo = dayjs().subtract(75, 'year');

      return birthDate.isBefore(seventyFiveYearsAgo);
    },
    verifyPriorConditions: function ({ event }) {
      return event.value.priorConditions.length === 0;
    },
  },
  actions: {
    reject: assign((_, params: { reason: string }) => ({
      rejected: true,
      rejectedReason: params.reason,
    })),
    clearRejected: assign(() => {
      return { rejected: false, rejectedReason: undefined };
    }),
    complete: assign(() => {
      return { questionnaireCompleted: true };
    }),
    storeQuestionnaire: assign(({ context, event }, params) => {
      context.questionnaire = {
        ...context.questionnaire,
        ...event?.value,
        ...params,
      };
      return context;
    }),
    store: assign(({ context, event }) => {
      return { ...context, ...event.value };
    }),
    storeClientSecret: assign(({ context, event }) => {
      return { ...context, paymentIntent: event.value };
    }),
  },
});

//base onboarding setup
export const onboardingMachineConfig = {
  context: {
    questionnaireCompleted: false,
    questionnaire: {},
  },
  id: 'onboarding',
  initial: 'questionnaire',
  meta: { total: 9 },
  states: {
    questionnaire: {},
    selectTreatmentType: {
      on: {
        next: {
          target: 'selectTreatment',
          actions: 'store',
        },
        back: {
          target: 'questionnaire.additionalInformation',
        },
      },
      meta: { step: 1, name: 'Virtual Doctor Visit' },
    },
    selectTreatment: {
      on: {
        next: {
          target: 'info',
          actions: 'store',
        },
        back: {
          target: 'selectTreatmentType',
        },
      },
      meta: { step: 2, name: 'Virtual Doctor Visit' },
    },
    info: {
      on: {
        next: {
          target: 'uploadIDPhoto',
        },
        back: {
          target: 'selectTreatment',
        },
      },
      meta: { step: 3, name: 'Virtual Doctor Visit' },
    },
    uploadIDPhoto: {
      on: {
        next: {
          target: 'uploadFacePhoto',
          actions: 'store',
        },
        back: {
          target: 'info',
        },
      },
      meta: { step: 4, name: 'Virtual Doctor Visit' },
    },
    uploadFacePhoto: {
      on: {
        next: {
          target: 'visitCompletion',
          actions: 'store',
        },
        back: {
          target: 'uploadIDPhoto',
        },
      },
      meta: { step: 5, name: 'Virtual Doctor Visit' },
    },
    visitCompletion: {
      on: {
        next: {
          target: 'summary',
        },
        back: {
          target: 'uploadFacePhoto',
        },
      },
      meta: { step: 6, name: 'Checkout' },
    },
    summary: {
      on: {
        next: {
          target: 'shipping',
        },
        back: {
          target: 'visitCompletion',
        },
      },
      meta: { step: 7, name: 'Checkout' },
    },
    shipping: {
      on: {
        next: {
          target: 'payment',
          actions: 'store',
        },
        back: {
          target: 'summary',
        },
      },
      meta: { step: 8, name: 'Checkout' },
    },
    payment: {
      on: {
        update: {
          actions: 'storeClientSecret',
        },
        complete: {
          target: 'onboarded',
        },
        back: {
          target: 'shipping',
        },
      },
      meta: { step: 9, name: 'Checkout' },
    },
    onboarded: {
      type: 'final',
    },
    meta: { step: 10 },
  },
};
