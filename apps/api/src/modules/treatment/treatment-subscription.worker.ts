import { runInDbTransaction } from '@/helpers/transaction';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { subMinutes, subSeconds } from 'date-fns';

import { PrismaService } from '../prisma/prisma.service';
import { TreatmentUpdatedEvent } from '../shared/events/treatment-topic.definition';
import { LilyDirectService } from '../subscription/lily-direct/lily-direct.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { TreatmentService } from './services/treatment.service';

@Injectable()
export class TreatmentSubscriptionWorker {
  private readonly logger = new Logger(TreatmentSubscriptionWorker.name);

  private isDisabled: boolean = true;

  constructor(
    private readonly prisma: PrismaService,
    private readonly lilyDirectService: LilyDirectService,
    private readonly treatmentService: TreatmentService,
    private readonly config: ConfigService,
  ) {
    // Check IS_CLI first, if true, disable this worker
    this.isDisabled =
      this.config.get('IS_CLI') === 'true' ||
      this.config.get('ENABLE_SUBSCRIPTION_WORKERS') !== 'true';
    if (this.isDisabled) {
      console.warn('LilyDirect worker is disabled, skipping renewal');
      return;
    }
  }

  @Cron(CronExpression.EVERY_30_SECONDS)
  async markLilyDirectTreatmentAsPaidIfActiveSubscription(): Promise<void> {
    if (this.isDisabled) {
      return;
    }

    const now = new Date();
    const past30Secs = subSeconds(now, 90);

    await runInDbTransaction(this.prisma, async (prisma) => {
      const prescriptions = await prisma.prescription.findMany({
        where: {
          status: 'queued',
          pharmacy: { name: 'Lily Direct' },
          createdAt: { lte: past30Secs },
        },
        select: {
          id: true,
          treatmentId: true,
          patientId: true,
          pharmacyId: true,
          productPrice: { select: { externalMappings: true } },
        },
      });

      for (const prescription of prescriptions) {
        const patientId = prescription.patientId;
        const treatmentId = prescription.treatmentId;

        const subscription = await this.lilyDirectService.hasActiveSubscription(
          { patientId },
        );
        if (!subscription) {
          this.logger.warn(
            `No active subscription found for patient ${patientId} for treatment ${treatmentId}`,
          );
          continue;
        }

        const treatmentEventsToEmit: {
          event: TreatmentUpdatedEvent['event'];
        }[] = [];
        const actor = await this.treatmentService.getActor(treatmentId, (e) => {
          treatmentEventsToEmit.push(e);
          console.log('[handleStripeInvoicePaidEvent] emitTreatmentEvent', e);
        });

        // Send the chargeSucceeded event
        actor.send({
          type: 'chargeSucceeded',
          externalMapping: prescription.pharmacyId,
        });

        await this.treatmentService.updateTreatmentRecord(actor, { prisma });

        // Update the prescription status to paid
        await prisma.prescription.update({
          where: { id: prescription.id },
          data: {
            status: 'paid',
            stripeInvoiceId: subscription.lastStripeInvoiceId,
            lastError: null,
          },
        });

        await prisma.patient.update({
          where: { id: prescription.patientId },
          data: { status: 'activePrescription' },
        });

        for (const { event } of treatmentEventsToEmit) {
          await this.treatmentService.emitTreatmentUpdatedEvent(
            event,
            treatmentId,
            { prisma },
          );
        }
      }
    });
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async markLilyDirectTreatmentAsFailedIfNoPaidInvoiceWithinTime(): Promise<void> {
    if (this.isDisabled) {
      return;
    }

    const now = new Date();

    console.log(
      '[markLilyDirectTreatmentAsFailedIfNoPaidInvoiceWithinTime] running',
      now,
    );

    await runInDbTransaction(this.prisma, async (prisma) => {
      const treatments = await prisma.treatment.findMany({
        where: {
          prescription: {
            some: {
              status: 'queued',
              createdAt: {
                lte: subMinutes(now, 30),
              },
              pharmacy: { name: 'Lily Direct' },
            },
          },
        },
        select: {
          id: true,
          patientId: true,
          prescription: {
            where: { status: 'queued' },
            select: {
              id: true,
            },
          },
        },
      });

      for (const treatment of treatments) {
        const prescription = treatment.prescription[0];
        if (!prescription) continue;

        const patientId = treatment.patientId;
        const subscription = await this.lilyDirectService.hasActiveSubscription(
          { patientId },
        );

        if (subscription) continue;

        for (const treatment of treatments) {
          this.logger.log(
            `Marking treatement ${treatment.id} as payment failed cause no subscription found after 30min`,
          );

          await this.treatmentService.markTreatmentAsPaiementFailed(
            treatment.id,
            { prisma },
          );
        }
      }
    });
  }
}
