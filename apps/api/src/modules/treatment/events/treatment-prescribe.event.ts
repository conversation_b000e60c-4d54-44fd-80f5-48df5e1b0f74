import type {
  MultiplePrescriptionResponse,
  PharmacyPatient,
  PharmacyPrescriber,
  PharmacyPrescriptionProduct,
  PrescriptionRequest,
} from '@modules/integrations/pharmacy';
import type { SegmentTrack } from '@modules/shared/types/events';
import type {
  Doctor,
  DoctorsOnState,
  Patient,
  PatientShippingAddress,
  State,
  Treatment,
  User,
} from '@prisma/client';
import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { TreatmentUpdatedQueueEvent } from '@/modules/shared/events/treatment-topic.definition';
import { AuditService } from '@modules/audit-log/audit-log.service';
import { PharmacyServiceFactory } from '@modules/integrations/pharmacy';
import { segmentTrackEvents } from '@modules/shared/events';
import {
  SlackChannelKey,
  SlackService,
} from '@modules/shared/services/slack.service';
import { TreatmentLockingService } from '@modules/treatment/services/treatment-locking.service';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Prisma } from '@prisma/client';
import { formatInTimeZone } from 'date-fns-tz';

import {
  PharmacyError,
  PharmacyTemporaryError,
} from '../errors/pharmacy.errors';
import {
  TreatmentMachineContext,
  TreatmentProduct,
} from '../states/treatment.state';

type PatientWithRelations = Patient & {
  user: User;
  state: State;
  doctor: Doctor & {
    user: User;
    state: State;
    prescribesIn: DoctorsOnState[];
  };
  shippingAddresses: (PatientShippingAddress & {
    state: State;
  })[];
};

@Injectable()
export class TreatmentPrescribeEventListener {
  private readonly logger = new Logger(TreatmentPrescribeEventListener.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly pharmacyServiceFactory: PharmacyServiceFactory,
    private readonly treatmentService: TreatmentService,
    private readonly auditService: AuditService,
    private readonly slackService: SlackService,
    private readonly lockingService: TreatmentLockingService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Handles event sent by the treatment state machine
   * Uses a patient-level lock to ensure only one worker processes treatments for a patient at once
   */
  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'treatment-prescribe-job',
    filter: ['prescription_prescribe'],
  })
  async handleTreatmentPrescribeEvent({ payload }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    const { patientId, treatmentId } = context;

    // Get current refill number
    const { currentRefill } = await this.prisma.treatment.findFirstOrThrow({
      where: { id: treatmentId },
      select: { currentRefill: true },
    });

    this.logger.log(
      `Handling treatment.prescription_prescribe event for treatment ${treatmentId}, refill ${currentRefill}`,
    );

    // Run entire flow under the patient‑level mutex to ensure all related treatments
    // are processed by the same worker
    const lockResult = await this.lockingService.withPatientLock(
      patientId,
      currentRefill,
      30_000, // 30-second TTL
      async () => {
        this.logger.verbose(
          `[LOCK] Acquired patient lock for ${patientId}, refill ${currentRefill} – prescribing treatment ${treatmentId}`,
        );

        const product = context.activeProduct!;

        // Get the external mapping from context or fail
        let selectedMapping = null;
        let failureReason = '';

        if (!context.externalMapping) {
          failureReason = `No external mapping ID found in context for treatment ${treatmentId}`;
        } else {
          // Fetch the specific external mapping with all necessary relationships
          selectedMapping = await this.prisma.productPriceMapping.findUnique({
            where: {
              id: context.externalMapping,
            },
            include: {
              productPrice: {
                include: {
                  product: {
                    include: {
                      pharmacy: true,
                    },
                  },
                },
              },
            },
          });

          if (!selectedMapping) {
            failureReason = `External mapping ${context.externalMapping} not found`;
          }
        }

        if (!selectedMapping) {
          this.logger.warn(failureReason);
          await this.emitTreatmentEvent(
            treatmentId,
            'prescribeFailed',
            failureReason,
            {
              treatment: null,
              patient: null,
              product,
            },
          );
          return true;
        }
        const pharmacySlug: string =
          selectedMapping.productPrice?.product?.pharmacy?.slug || 'dosespot';

        // Check if this treatment has already been processed
        const lockStatus = await this.lockingService.tryAcquireProcessingLock(
          treatmentId,
          currentRefill,
          120, // 120 seconds TTL
        );

        if (!lockStatus.canProcess) {
          this.logger.verbose(
            `Treatment ${treatmentId}, refill ${currentRefill} is already being processed or has been processed`,
          );
          return true;
        }

        const treatmentsToProcess = await runInDbTransaction(
          this.prisma,
          async (prisma) => {
            const treatment = await prisma.treatment.findFirstOrThrow({
              where: { id: treatmentId },
              include: { patient: { select: { stateId: true } } },
            });

            const related = await this.findRelatedTreatmentsForPrescription(
              prisma,
              patientId,
              treatmentId,
            );

            this.logger.log(
              `Found ${related.length} related treatments for patient ${patientId}`,
            );

            // Check and lock related treatments
            for (const r of related) {
              const relatedLockStatus =
                await this.lockingService.tryAcquireProcessingLock(
                  r.treatmentId,
                  currentRefill,
                );

              if (!relatedLockStatus.canProcess) {
                this.logger.verbose(
                  `Related treatment ${r.treatmentId} already being processed - excluding from batch`,
                );
              }
            }

            return [
              { treatment, treatmentId, product, selectedMapping },
              ...related,
            ];
          },
        );

        if (treatmentsToProcess.length === 0) {
          this.logger.warn(`No treatments to process for patient ${patientId}`);
          return true;
        }

        // Get detailed patient information
        const patient = await this.prisma.patient.findFirstOrThrow({
          where: { id: patientId },
          include: {
            shippingAddresses: {
              where: { default: true },
              include: { state: true },
              take: 1,
            },
            doctor: {
              include: {
                user: true,
                state: true,
                prescribesIn: {
                  where: {
                    stateId: {
                      equals: treatmentsToProcess[0].treatment.patient.stateId,
                    },
                  },
                  include: { state: true },
                },
              },
            },
            user: true,
            state: true,
          },
        });

        // Get the pharmacy service for this mapping
        const pharmacyService =
          this.pharmacyServiceFactory.getService(pharmacySlug);

        // Build prescription requests for all treatments
        const prescriptionRequests: PrescriptionRequest[] = [];

        for (const treatmentToProcess of treatmentsToProcess) {
          try {
            // Get the prescription for this treatment
            const prescription = await this.prisma.prescription.findFirst({
              where: {
                treatmentId: treatmentToProcess.treatmentId,
                status: 'paid',
              },
              orderBy: { createdAt: 'desc' },
            });

            if (!prescription) {
              throw new Error(
                `No prescription found for treatment ${treatmentToProcess.treatmentId}`,
              );
            }

            // Build the prescription request
            const prescriptionRequest = await this.buildPrescriptionRequest(
              treatmentToProcess,
              patient,
              prescription.id,
            );

            prescriptionRequests.push(prescriptionRequest);
          } catch (error) {
            this.logger.error(
              `Failed to prepare prescription for treatment ${treatmentToProcess.treatmentId}:`,
              error,
            );
          }
        }

        // If no prescriptions to process, return early
        if (prescriptionRequests.length === 0) {
          this.logger.warn(
            `No prescriptions to submit for patient ${patientId}`,
          );
          return true;
        }

        // Submit all prescriptions together
        let response: MultiplePrescriptionResponse;
        try {
          response = await pharmacyService.submitPrescriptions({
            prescriptions: prescriptionRequests,
          });
        } catch (error) {
          this.logger.error(
            'Failed to submit prescriptions to pharmacy',
            error,
          );
          // Handle complete failure - all prescriptions failed
          const results = prescriptionRequests.map((req) => ({
            treatmentId: req.treatmentId,
            success: false,
            error: error.message,
            isRetryable:
              error instanceof PharmacyError ? error.isRetryable : true,
          }));

          // Process failure events
          for (const result of results) {
            const treatment = treatmentsToProcess.find(
              (t) => t.treatmentId === result.treatmentId,
            );

            await this.emitTreatmentEvent(
              result.treatmentId,
              'prescribeFailed',
              result.error,
              {
                treatment: treatment.treatment,
                patient,
                product: treatment.product,
              },
            );
          }

          throw error;
        }

        // Process the response for each prescription
        const results = response.results || [];

        // Update state machine for each treatment based on results
        for (const result of results) {
          const treatment = treatmentsToProcess.find(
            (t) => t.treatmentId === result.treatmentId,
          );

          if (result.success) {
            await this.emitTreatmentEvent(
              result.treatmentId,
              'prescribeSucceeded',
              undefined,
              {
                treatment: treatment.treatment,
                patient,
                product: treatment.product,
              },
            );
            await this.lockingService.markAsProcessed(
              result.treatmentId,
              currentRefill,
            );

            // Log the success for audit purposes
            await this.auditService.append({
              patientId: patient.id,
              action: 'TREATMENT_ORDER_SENT',
              actorType: 'SYSTEM',
              actorId: 'prescription-service',
              resourceType: 'TREATMENT',
              resourceId: result.treatmentId,
              details: {
                pharmacy: pharmacySlug,
                orderId: result.orderId || result.pharmacyOrderId || '',
                product: {
                  name: treatment.product.name,
                  id: treatment.product.id,
                  quantity: 1,
                },
              },
            });

            // Emit orderSent event to Segment
            const orderSentEvent: SegmentTrack = {
              event: segmentTrackEvents.orderSent.name,
              userId: patient.id,
              properties: {
                productName: treatment.product.form
                  ? `${treatment.product.name}, ${treatment.product.form}`
                  : treatment.product.name,
                doctorName: `${patient.doctor.user.firstName} ${patient.doctor.user.lastName}`,
                doctorId: patient.doctor.id,
                pharmacy: treatment.product.pharmacy,
                email: patient.user.email,
                type: 'automatic',
              },
            };

            this.eventEmitter.emit(
              segmentTrackEvents.orderSent.event,
              orderSentEvent,
            );
          } else {
            await this.emitTreatmentEvent(
              result.treatmentId,
              'prescribeFailed',
              result.message || 'Unknown error',
              {
                treatment: treatment.treatment,
                patient,
                product: treatment.product,
              },
            );
          }
        }

        // Check if we should retry - only if there are retryable failures
        const hasRetryableFailures = results.some(
          (r) =>
            !r.success &&
            r.errors?.some(
              (e) =>
                e.message?.includes('retry') ||
                e.message?.includes('temporary'),
            ),
        );
        const hasSuccesses = results.some((r) => r.success);

        // If all treatments failed and at least one is retryable, throw to trigger SNS/SQS retry
        if (!hasSuccesses && hasRetryableFailures) {
          throw new PharmacyTemporaryError(
            'All treatments failed with retryable errors',
            pharmacySlug,
          );
        }

        // If we have only permanent failures, don't throw (no point in retrying)
        // The individual treatments have already been marked as failed

        return true;
      },
    );

    // If lock wasn't obtained, return success - another worker is handling it
    if (lockResult === null) {
      this.logger.warn(
        `[LOCK] Could not acquire patient lock for ${patientId}, refill ${currentRefill} – returning success (another worker is handling)`,
      );
      // Return success - another worker is processing this patient's treatments
      return;
    }
  }

  /**
   * Find related treatments for the same patient that need to be prescribed
   * This method uses a different approach to find treatments that were recently charged
   * and are waiting to be prescribed, even if they're not in the 'prescribing' state yet
   */
  private async findRelatedTreatmentsForPrescription(
    prisma: Prisma.TransactionClient,
    patientId: string,
    currentTreatmentId: string,
  ): Promise<
    Array<{
      treatment: any;
      treatmentId: string;
      product: TreatmentProduct;
      selectedMapping: any;
    }>
  > {
    // Find the current treatment's prescription
    const currentPrescription = await prisma.prescription.findFirst({
      where: { treatmentId: currentTreatmentId },
      orderBy: { createdAt: 'desc' },
    });

    if (!currentPrescription) return [];

    // Get the invoice ID from the current treatment's prescription
    const invoiceId = currentPrescription.stripeInvoiceId;

    if (!invoiceId) return [];

    // Find other treatments for the same patient that are in the same invoice
    const relatedPrescriptions = await prisma.prescription.findMany({
      where: {
        stripeInvoiceId: invoiceId,
        treatment: {
          id: { not: currentTreatmentId },
          patientId: patientId,
        },
      },
      include: {
        treatment: {
          include: {
            patient: { select: { stateId: true } },
          },
        },
      },
    });

    const result = [];

    for (const prescription of relatedPrescriptions) {
      const treatment = prescription.treatment;

      // Extract the context from the state
      const state = treatment.state as any;
      const context = state.context as TreatmentMachineContext;

      if (!context.activeProduct) {
        this.logger.warn(
          `[WARN] Treatment ${treatment.id} has no active product, skipping`,
        );
        continue;
      }

      // For related treatments, use their external mapping from context if available
      if (context.externalMapping) {
        const relatedMapping = await prisma.productPriceMapping.findUnique({
          where: {
            id: context.externalMapping,
          },
          include: {
            productPrice: {
              include: {
                product: {
                  include: {
                    pharmacy: true,
                  },
                },
              },
            },
          },
        });

        if (relatedMapping) {
          result.push({
            treatment,
            treatmentId: treatment.id,
            product: context.activeProduct,
            selectedMapping: relatedMapping,
          });
        } else {
          this.logger.warn(
            `Related treatment ${treatment.id} has external mapping ${context.externalMapping} but mapping not found`,
          );
        }
      } else {
        this.logger.warn(
          `Related treatment ${treatment.id} has no external mapping in context`,
        );
      }
    }

    return result;
  }

  /**
   * Build a PrescriptionRequest from treatment and patient data
   */
  private async buildPrescriptionRequest(
    treatmentToProcess: {
      treatment: Treatment;
      treatmentId: string;
      product: TreatmentProduct;
      selectedMapping: any;
    },
    patient: PatientWithRelations,
    prescriptionId: string,
  ): Promise<PrescriptionRequest> {
    const onboardingState = patient.onboardingState as any;
    const allergies = onboardingState?.context?.questionnaire?.allergies || [];

    // Build patient data
    const pharmacyPatient: PharmacyPatient = {
      id: patient.id,
      firstName: patient.user.firstName,
      lastName: patient.user.lastName,
      dateOfBirth: new Date(patient.birthDate!).toISOString().split('T')[0],
      gender: patient.gender || 'unknown',
      address: {
        street1: patient.shippingAddresses[0]?.address1 || '',
        street2: patient.shippingAddresses[0]?.address2 || undefined,
        city: patient.shippingAddresses[0]?.city || '',
        state: patient.shippingAddresses[0]?.state?.code || '',
        zipCode: patient.shippingAddresses[0]?.zip || '',
        country: 'US',
      },
      phoneNumber: patient.user.phone || '',
      email: patient.user.email || undefined,
      knownAllergies: allergies,
    };

    // Build prescriber data
    const pharmacyPrescriber: PharmacyPrescriber = {
      id: patient.doctor.id,
      npi: patient.doctor.npiNumber || '',
      firstName: patient.doctor.user.firstName,
      lastName: patient.doctor.user.lastName,
      deaNumber: undefined, // DEA number not in schema
      stateLicenseNumber:
        patient.doctor.prescribesIn[0]?.licenseNumber || undefined,
      address: {
        street1: patient.doctor.address1,
        street2: patient.doctor.address2 || undefined,
        city: patient.doctor.city,
        state: patient.doctor.state.code || '',
        zipCode: patient.doctor.zip,
        country: 'US',
      },
      phoneNumber: patient.doctor.primaryPhone || undefined,
    };

    // Build product data
    // Always fetch ALL mappings for the product price to support pharmacies that require multiple items
    const pharmacySlug =
      treatmentToProcess.selectedMapping?.productPrice?.product?.pharmacy
        ?.slug || 'dosespot';

    let pharmacyProducts: PharmacyPrescriptionProduct[] = [];

    // Fetch all mappings for this product price
    const allMappings = await this.prisma.productPriceMapping.findMany({
      where: {
        productPriceId: treatmentToProcess.product.id,
      },
    });

    if (allMappings.length === 0) {
      throw new Error(
        `No ProductPriceMappings found for product ${treatmentToProcess.product.id}`,
      );
    }

    // Create a product for each mapping
    // Most pharmacies will have one mapping, but some (like Empower) may have multiple (e.g., syringe + medication vial)
    pharmacyProducts = allMappings.map((mapping) => {
      const mappingMetadata = mapping.metadata as any;

      return {
        id: treatmentToProcess.product.id,
        name: mapping.name || treatmentToProcess.product.name,
        dose: treatmentToProcess.product.dose,
        form: treatmentToProcess.product.form?.toString() || '',
        externalId: mapping.externalId,
        drugDescription: mapping.name || treatmentToProcess.product.name,
        quantity: Number(mappingMetadata?.quantity) || 1,
        daysSupply:
          Number(mappingMetadata?.daysSupply) ||
          treatmentToProcess.product.days,
        sig:
          mappingMetadata?.directions ||
          mappingMetadata?.sig ||
          'Take as directed',
        refills: 0, // We don't send refills, we manage them internally
        originalProductDetails: treatmentToProcess.product,
        metadata: mappingMetadata,
      };
    });

    return {
      treatmentId: treatmentToProcess.treatmentId,
      prescriptionId,
      patient: pharmacyPatient,
      prescriber: pharmacyPrescriber,
      products: pharmacyProducts,
      pharmacyIdentifier: pharmacySlug,
      prescriptionIssueDate: formatInTimeZone(
        new Date(),
        'America/Los_Angeles',
        'yyyy-MM-dd',
      ),
      originalTreatmentDetails: treatmentToProcess.treatment,
    };
  }

  /**
   * Emit event to the treatment state machine and update the treatment record
   */
  private async emitTreatmentEvent(
    treatmentId: string,
    eventType: 'prescribeSucceeded' | 'prescribeFailed',
    errorMessage?: string,
    context?: {
      patient: PatientWithRelations;
      treatment: Treatment;
      product: TreatmentProduct;
    },
  ): Promise<void> {
    const actor = await this.treatmentService.getActor(treatmentId, (e) =>
      console.log(`[handleTreatmentPrescribeEvent] emitTreatmentEvent`, e),
    );

    actor.send({ type: eventType });

    await this.treatmentService.updateTreatmentRecord(actor);

    if (eventType === 'prescribeSucceeded') {
      this.logger.log(`Prescription succeeded for treatment ${treatmentId}`);
    } else {
      this.logger.error(
        `Prescription failed for treatment ${treatmentId}${errorMessage ? ': ' + errorMessage : ''}`,
      );

      // Only send Slack notification if not related to Empower API
      // (which is already handled by the Empower service)
      if (context && context.patient && !errorMessage?.includes('Empower')) {
        const patientName = `${context.patient.user.firstName} ${context.patient.user.lastName}`;
        const doctorName = `${context.patient.doctor.user.firstName} ${context.patient.doctor.user.lastName}`;
        const product = `${context.product.name}, ${context.product.dose}, ${context.product.form}`;

        const slackMessage = {
          blocks: [
            {
              type: 'header',
              text: {
                type: 'plain_text',
                text: '❌ Prescription Submission Failed',
                emoji: true,
              },
            },
            {
              type: 'section',
              fields: [
                { type: 'mrkdwn', text: `*Treatment ID:*\n${treatmentId}` },
                { type: 'mrkdwn', text: `*Patient:*\n${patientName}` },
              ],
            },
            {
              type: 'section',
              fields: [
                { type: 'mrkdwn', text: `*Product:*\n${product}` },
                { type: 'mrkdwn', text: `*Doctor:*\n${doctorName}` },
              ],
            },
            {
              type: 'section',
              text: { type: 'mrkdwn', text: `*Error:*\n${errorMessage}` },
            },
          ],
          text: `Prescription failed for ${patientName} (Treatment ID: ${treatmentId}): ${errorMessage}`,
        };

        await this.slackService.sendNotification(
          slackMessage,
          SlackChannelKey.PHARMACY_INTEGRATION,
        );
      }
    }
  }
}
