import { PrismaService } from '@/modules/prisma/prisma.service';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { TreatmentUpdatedQueueEvent } from '@/modules/shared/events/treatment-topic.definition';
import { segmentIdentifyEvent } from '@modules/shared/events';
import { SegmentIdentify } from '@modules/shared/types/events';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { prescriptionStatus } from '@prisma/client';

import { TreatmentMachineContext } from '../states/treatment.state';

@Injectable()
export class TreatmentPrescriptionCreateEventListener {
  private logger = new Logger(TreatmentPrescriptionCreateEventListener.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Handles event sent by the treatment state machine
   * @param payload
   */
  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'treatment-prescription-create-job',
    filter: ['prescription_create'],
  })
  async handleTreatmentCreateEvent({ payload }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };

    const { patientId, treatmentId } = context;
    const product = context.activeProduct!;
    const invoiceId = (context as unknown as { invoiceId: string }).invoiceId;

    await this.prisma.$transaction(async (prisma) => {
      const patient = await prisma.patient.findUniqueOrThrow({
        where: { id: patientId },
        include: { doctor: true, user: true },
      });

      if (!patient.doctor) {
        throw new Error('Patient does not have an assigned doctor');
      }

      const productPrice = await prisma.productPrice.findUniqueOrThrow({
        where: { id: product.id },
        select: {
          metadata: true,
          product: { select: { id: true, metadata: true } },
        },
      });

      if (invoiceId) {
        const existingPrescriptions = await prisma.prescription.findMany({
          where: {
            treatmentId,
            stripeInvoiceId: invoiceId,
            status: prescriptionStatus.failed,
          },
        });

        if (existingPrescriptions.length) {
          const ids = existingPrescriptions.map((p) => p.id);
          await prisma.prescription.updateMany({
            where: { id: { in: ids }, stripeInvoiceId: invoiceId },
            data: {
              status: prescriptionStatus.queued,
              updatedAt: new Date(),
              lastError: null,
            },
          });
          console.log(
            `Updated existing prescriptions ${ids.join(', ')} to queued status`,
          );
          this.logger.debug(
            `Updated existing prescriptions ${ids.join(', ')} to queued status`,
          );
          return;
        }
      }

      // If not retrying or no existing failed prescription found, create a new one
      await prisma.prescription.create({
        data: {
          patient: { connect: { id: patientId } },
          doctor: { connect: { id: patient.doctor.id } },
          treatment: { connect: { id: treatmentId } },
          product: { connect: { id: productPrice.product.id } },
          productPrice: { connect: { id: product.id } },
          pharmacy: { connect: { id: patient.pharmacyId } },
          status: prescriptionStatus.queued,
          refill: context.currentRefill,
          stripeCouponId: product.coupon,
          stripeInvoiceId: null,
        },
      });
      this.logger.log('Created new prescription');

      // send current _core_ treatment identify to Segment
      if (productPrice.product.metadata['type'] === 'core') {
        const identifyEvent: SegmentIdentify = {
          userId: patient.user.id,
          traits: { currentTreatment: productPrice.metadata['label'] },
        };
        this.eventEmitter.emit(
          segmentIdentifyEvent.analyticIdentify,
          identifyEvent,
        );
      }
    });
  }
}
