import { PreventImpersonatedEditGuard } from '@modules/auth/guards/prevent-impersonated-edit.guard';
import { DoctorService } from '@modules/doctor/doctor.service';
import { MoveRefillDateDto } from '@modules/treatment/dto/move-refill-date.dto';
import { TreatmentPauseDto } from '@modules/treatment/dto/treatment-pause.dto';
import { TreatmentDto } from '@modules/treatment/dto/treatment.dto';
import { ProcessDraftPrescriptionsJob } from '@modules/treatment/jobs/process-draft-prescriptions.job';
import { SendNotificationsJob } from '@modules/treatment/jobs/send-notifications.job';
import { UpdateTreatmentsJob } from '@modules/treatment/jobs/update-treatments.job';
import { GetTreatmentUseCase } from '@modules/treatment/use-cases/get-treatment.use-case';
import { TreatmentCancelUseCase } from '@modules/treatment/use-cases/treatment-cancel.use-case';
import { TreatmentCreateUseCase } from '@modules/treatment/use-cases/treatment-create.use-case';
import { TreatmentFireNextUseCase } from '@modules/treatment/use-cases/treatment-fire-next.use-case';
import { TreatmentListUseCase } from '@modules/treatment/use-cases/treatment-list.use-case';
import { TreatmentMarkAsPrescribedUseCase } from '@modules/treatment/use-cases/treatment-mark-as-prescribed.use-case';
import { TreatmentMoveRefillDateUseCase } from '@modules/treatment/use-cases/treatment-move-refill-date.use-case';
import { TreatmentPauseUseCase } from '@modules/treatment/use-cases/treatment-pause.use-case';
import { TreatmentResumeUseCase } from '@modules/treatment/use-cases/treatment-resume.use-case';
import { TreatmentStartUseCase } from '@modules/treatment/use-cases/treatment-start.use-case';
import {
  BadRequestException,
  Body,
  Controller,
  ForbiddenException,
  Get,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { addDays, isWithinInterval, parseISO } from 'date-fns';
import { Request } from 'express';

import { AdminService } from '../admin/admin.service';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { roles } from '../auth/types/roles';
import { CreateTreatmentArrayDTO } from './dto/create-treatment.dto';
import { GetTreatmentLogsUseCase } from './use-cases/get-treatment-logs.use-case';
import { TreatmentRetryFailedPaymentUseCase } from './use-cases/treatment-retry-failed–payment.use-case';

@Controller('treatment')
@UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
@Roles([roles.Doctor, roles.Admin])
export class TreatmentController {
  constructor(
    private readonly doctorService: DoctorService,
    private readonly adminService: AdminService,
    private readonly treatmentCancelUseCase: TreatmentCancelUseCase,
    private readonly treatmentCreateUseCase: TreatmentCreateUseCase,
    private readonly treatmentFireNextUseCase: TreatmentFireNextUseCase,
    private readonly getTreatmentUseCase: GetTreatmentUseCase,
    private readonly treatmentListUseCase: TreatmentListUseCase,
    private readonly treatmentMarkAsPrescribedUseCase: TreatmentMarkAsPrescribedUseCase,
    private readonly treatmentMoveRefillDateUseCase: TreatmentMoveRefillDateUseCase,
    private readonly treatmentPauseUseCase: TreatmentPauseUseCase,
    private readonly treatmentResumeUseCase: TreatmentResumeUseCase,
    private readonly treatmentStartUseCase: TreatmentStartUseCase,
    private readonly updateTreatmentsJob: UpdateTreatmentsJob,
    private readonly processDraftPrescriptionsJob: ProcessDraftPrescriptionsJob,
    private readonly sendNotificationsJob: SendNotificationsJob,
    private readonly treatmentRetryUseCase: TreatmentRetryFailedPaymentUseCase,
    private readonly getTreatmentLogsUseCase: GetTreatmentLogsUseCase,
  ) {}

  @Get('list/:patientId')
  async listTreatments(@Param('patientId') patientId: string) {
    try {
      if (!patientId) {
        throw new BadRequestException('Patient ID is required');
      }
      return await this.treatmentListUseCase.execute(patientId);
    } catch (error) {
      console.error(`Error listing treatments for patient ${patientId}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to list treatments');
    }
  }

  @Get(':treatmentId')
  async getTreatment(@Param('treatmentId') treatmentId: string) {
    try {
      if (!treatmentId) {
        throw new BadRequestException('Treatment ID is required');
      }
      return await this.getTreatmentUseCase.execute(treatmentId);
    } catch (error) {
      console.error(`Error getting treatment ${treatmentId}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get treatment details');
    }
  }

  @Post('create')
  async create(
    @Body() requestBody: CreateTreatmentArrayDTO,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      if (!userId) {
        throw new ForbiddenException('User ID not found in request');
      }

      const doctor = await this.doctorService.getData(userId);
      if (!doctor) {
        throw new NotFoundException('Doctor not found');
      }

      return await this.treatmentCreateUseCase.execute(doctor.id, requestBody);
    } catch (error) {
      console.error('Error creating treatment');
      if (
        error instanceof ForbiddenException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create treatment');
    }
  }

  @Post('start')
  async start(@Body() body: TreatmentDto) {
    try {
      if (!body.treatmentId) {
        throw new BadRequestException('Treatment ID is required');
      }
      return await this.treatmentStartUseCase.execute(body);
    } catch (error) {
      console.error(`Error starting treatment ${body.treatmentId}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to start treatment');
    }
  }

  @Post('retry-payment')
  async retry(@Body() body: TreatmentDto, @Req() request: Request) {
    try {
      const userId = request.user['userId'];
      const role = request.user['role'];
      let actorId = '';

      if (role === roles.Doctor) {
        const doctor = await this.doctorService.getData(userId);
        actorId = doctor.id;
      } else if (role === roles.Admin) {
        const admin = await this.adminService.getProfile(userId);
        actorId = admin.id;
      }

      if (!body.treatmentId) {
        throw new BadRequestException('Treatment ID is required');
      }
      return await this.treatmentRetryUseCase.execute(body, {
        actorId,
        actorType: role,
      });
    } catch (error) {
      console.error(`Error retrying treatment ${body.treatmentId}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retry treatment');
    }
  }

  @Post('prescribed')
  async prescribed(@Body() body: TreatmentDto) {
    try {
      if (!body.treatmentId) {
        throw new BadRequestException('Treatment ID is required');
      }
      return await this.treatmentMarkAsPrescribedUseCase.execute(body);
    } catch (error) {
      console.error(
        `Error marking treatment ${body.treatmentId} as prescribed`,
      );
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to mark treatment as prescribed',
      );
    }
  }

  @Post('move-refill-date')
  async moveRefillDate(
    @Body() body: MoveRefillDateDto,
    @Req() request: Request,
  ) {
    try {
      const userId = request.user['userId'];
      const role = request.user['role'];
      let actorId = '';

      if (role === roles.Doctor) {
        const doctor = await this.doctorService.getData(userId);
        actorId = doctor.id;
      } else if (role === roles.Admin) {
        const admin = await this.adminService.getProfile(userId);
        actorId = admin.id;
      }

      if (!body.treatmentId || !body.date) {
        throw new BadRequestException('Treatment ID and new date are required');
      }
      return await this.treatmentMoveRefillDateUseCase.execute(body, {
        actorId,
        actorType: role,
      });
    } catch (error) {
      console.error(
        `Error moving refill date for treatment ${body.treatmentId}`,
      );
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to move refill date');
    }
  }

  @Post('fire-next')
  async fireNext(@Body() body: TreatmentDto, @Req() request: Request) {
    try {
      const userId = request.user['userId'];
      const role = request.user['role'];
      let actorId = '';

      if (role === roles.Doctor) {
        const doctor = await this.doctorService.getData(userId);
        actorId = doctor.id;
      } else if (role === roles.Admin) {
        const admin = await this.adminService.getProfile(userId);
        actorId = admin.id;
      }

      if (!body.treatmentId) {
        throw new BadRequestException('Treatment ID is required');
      }
      return await this.treatmentFireNextUseCase.execute(body, {
        actorId,
        actorType: role,
      });
    } catch (error) {
      console.error(`Error firing next treatment ${body.treatmentId}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to fire next treatment');
    }
  }

  @Post('pause')
  async pauseTreatment(
    @Body() body: TreatmentPauseDto,
    @Req() request: Request,
  ) {
    try {
      const userId = request.user['userId'] as string;
      const role = request.user['role'];
      let actorId = '';

      if (role === roles.Doctor) {
        const doctor = await this.doctorService.getData(userId);
        actorId = doctor.id;
      } else if (role === roles.Admin) {
        const admin = await this.adminService.getProfile(userId);
        actorId = admin.id;
        if (body.date) {
          const now = new Date();
          const maxDate = addDays(now, 30);
          const pauseDate = parseISO(body.date);
          if (!isWithinInterval(pauseDate, { start: now, end: maxDate })) {
            throw new BadRequestException(
              'Pause date must indefinite or be within the next 30 days for admins',
            );
          }
        }
      }

      if (!body.treatmentId) {
        throw new BadRequestException('Treatment ID is required');
      }
      return await this.treatmentPauseUseCase.execute(body, {
        actorId,
        actorType: role,
      });
    } catch (error) {
      console.error(`Error pausing treatment ${body.treatmentId}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to pause treatment');
    }
  }

  @Post('resume')
  async resumeTreatment(@Body() body: TreatmentDto, @Req() request: Request) {
    try {
      const userId = request.user['userId'] as string;
      const role = request.user['role'];
      let actorId = '';

      if (role === roles.Doctor) {
        const doctor = await this.doctorService.getData(userId);
        actorId = doctor.id;
      } else if (role === roles.Admin) {
        throw new ForbiddenException(
          'Admins are not allowed to unpause treatments',
        );
      }

      if (!body.treatmentId) {
        throw new BadRequestException('Treatment ID is required');
      }
      return await this.treatmentResumeUseCase.execute(body, {
        actorId,
        actorType: role,
      });
    } catch (error) {
      console.error(`Error resuming treatment ${body.treatmentId}`);
      if (
        error instanceof BadRequestException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to resume treatment');
    }
  }

  @Post('cancel')
  async cancelTreatment(@Body() body: TreatmentDto, @Req() request: Request) {
    try {
      const userId = request.user['userId'] as string;
      const role = request.user['role'];
      let actorId = '';

      if (role === roles.Doctor) {
        const doctor = await this.doctorService.getData(userId);
        actorId = doctor.id;
      } else if (role === roles.Admin) {
        const admin = await this.adminService.getProfile(userId);
        actorId = admin.id;
      }

      if (!body.treatmentId) {
        throw new BadRequestException('Treatment ID is required');
      }
      return await this.treatmentCancelUseCase.execute(body, {
        actorId,
        actorType: role,
      });
    } catch (error) {
      console.error(`Error canceling treatment ${body.treatmentId}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to cancel treatment');
    }
  }

  @Post('update-treatments')
  @Roles([roles.Doctor])
  async updateTreatmentsManually(): Promise<void> {
    try {
      if (process.env.ENVIRONMENT === 'production') {
        throw new ForbiddenException(
          'This endpoint is not available in production',
        );
      }
      await this.updateTreatmentsJob.run();
    } catch (error) {
      console.error('Error running update treatments job');
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update treatments');
    }
  }

  @Post('process-draft-prescriptions')
  @Roles([roles.Doctor])
  async processDraftPrescriptionsManually(): Promise<void> {
    try {
      if (process.env.ENVIRONMENT === 'production') {
        throw new ForbiddenException(
          'This endpoint is not available in production',
        );
      }
      await this.processDraftPrescriptionsJob.run();
    } catch (error) {
      console.error('Error processing draft prescriptions');
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to process draft prescriptions',
      );
    }
  }

  @Post('send-notifications')
  @Roles([roles.Doctor])
  async sendNotificationsManually(): Promise<void> {
    try {
      if (process.env.ENVIRONMENT === 'production') {
        throw new ForbiddenException(
          'This endpoint is not available in production',
        );
      }
      await this.sendNotificationsJob.run();
    } catch (error) {
      console.error('Error sending notifications');
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to send notifications');
    }
  }

  @Get(':treatmentId/logs')
  @Roles([roles.Admin])
  async getTreatmentLogs(@Param('treatmentId') treatmentId: string) {
    try {
      return await this.getTreatmentLogsUseCase.execute(treatmentId);
    } catch (error) {
      console.error(`Error getting logs for treatment ${treatmentId}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get treatment logs');
    }
  }
}
