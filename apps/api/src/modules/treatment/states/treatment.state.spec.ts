import { TreatmentActor } from '@modules/treatment/services/treatment.service';
import {
  createTreatmentMachine,
  daysLeftForNotification,
  TreatmentMachineInput,
} from '@modules/treatment/states/treatment.state';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as dayjs from 'dayjs';
import { omit } from 'ramda';
import { createActor } from 'xstate';

// Mock dayjs
jest.mock('dayjs', () => {
  const original = jest.requireActual('dayjs');
  return (date) => {
    if (date === undefined) {
      return original(mockNow);
    }
    return original(date);
  };
});

let mockNow: string;
let eventEmitter: jest.Mocked<EventEmitter2>;
let defaultInput: TreatmentMachineInput;
let machine: any;
let actor: TreatmentActor;

const createDefaultInput = (numProducts: number): TreatmentMachineInput => ({
  patientId: 'patient1',
  treatmentId: 'treatment1',
  pharmacy: 'pharmacy',
  isCore: true,
  refills: numProducts - 1,
  vials: 1,
  refillSystem: 'scaling',
  productPricesList: Array.from({ length: numProducts }, (_, i) => ({
    id: `price${i + 1}`,
    price: 10000 + i * 5000,
    name: `Product ${i + 1}`,
    days: 28,
    dose: `${i + 1}mg`,
    dosageLabel: `${i + 1}mg`,
    form: 'injectable',
    pharmacy: 'pharmacy',
  })),
});

const context = (actor: TreatmentActor) => actor.getSnapshot().context;
const state = (actor: TreatmentActor) => actor.getSnapshot().value;

describe.each([
  {
    name: 'with regular time elapsing',
    advanceTime: (duration: number) => {
      const dayjs = jest.requireActual('dayjs');
      mockNow = dayjs(mockNow).add(duration, 'millisecond').toISOString();
      jest.advanceTimersByTime(duration);
    },
  },
  {
    name: 'with time shift',
    advanceTime: (duration: number) => {
      actor.send({ type: 'shiftDateForward', offset: duration / 1000 });
    },
  },
])('Treatment State Machine $name', ({ advanceTime }) => {
  beforeEach(() => {
    jest.useFakeTimers();
    mockNow = '2023-01-01T00:00:00.000Z';
    eventEmitter = {
      emit: jest.fn(),
    } as unknown as jest.Mocked<EventEmitter2>;
  });

  const advanceTimeInDays = (days: number) => {
    return advanceTime(days * 24 * 60 * 60 * 1000);
  };

  describe('Given a Regular Treatment', () => {
    beforeEach(() => {
      defaultInput = createDefaultInput(3);
      machine = createTreatmentMachine((e) => {});
      actor = createActor(machine, { input: defaultInput });
      actor.start();
    });

    it('should complete the treatment successfully', () => {
      for (let i = 0; i <= context(actor).refills; i++) {
        expect(state(actor)).toEqual({
          inProgress: 'readyToCharge',
        });

        if (i === 0) {
          actor.send({ type: 'next' });
          expect(state(actor)).toEqual({
            inProgress: 'readyToCharge',
          });
          advanceTime(2 * 60 * 1000); // 2 minutes
        }

        actor.send({ type: 'next' });
        expect(state(actor)).toEqual({ inProgress: 'charging' });
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'treatment.prescriptionCreate',
          expect.any(Object),
        );

        // Simulate successful charge
        actor.send({ type: 'chargeSucceeded' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingForPrescription',
        });

        // Simulate prescription
        actor.send({ type: 'prescribed' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingBetweenRefills',
        });

        advanceTimeInDays(28);
        if (i > 0) advanceTime(2 * 60 * 1000); // 2 minutes

        actor.send({ type: 'next' });
      }

      // Final prescription should complete the treatment
      expect(state(actor)).toBe('completed');
      expect(context(actor).completedAt).not.toBeNull();
    });

    it('should set correct context values throughout the treatment', () => {
      // Check initial context
      expect(context(actor)).toMatchObject({
        patientId: 'patient1',
        treatmentId: 'treatment1',
        currentRefill: 0,
        refills: context(actor).refills,
        inProgressSince: mockNow,
        lastChargedAt: null,
        pausedAt: null,
      });

      // Start treatment
      expect(context(actor).nextEventIn).not.toBeNull();

      advanceTime(2 * 60 * 1000); // 2 minutes
      actor.send({ type: 'next' });

      expect(context(actor).nextEventIn).toBeNull();

      // Check context after starting
      expect(context(actor).inProgressSince).not.toBeNull();
      expect(context(actor).activeProduct).toEqual(
        expect.objectContaining({
          id: 'price1',
          price: 10000,
          name: 'Product 1',
        }),
      );

      // Loop through refills
      for (let i = 0; i <= context(actor).refills; i++) {
        // Simulate charge and prescription for current refill
        actor.send({ type: 'next' });
        actor.send({ type: 'chargeSucceeded' });
        actor.send({ type: 'prescribed' });

        expect(context(actor).nextEventIn).not.toBeNull();

        // Check context after refill
        expect(context(actor).lastChargedAt).not.toBeNull();
        expect(context(actor).currentRefill).toBe(i);

        const nextEvent = dayjs(mockNow)
          .add(28, 'day')
          .subtract(i === 0 ? 2 : 0, 'minutes')
          .toISOString();
        expect(context(actor).nextEventIn).toBe(nextEvent);

        // Advance to next refill
        advanceTimeInDays(28);
        if (i > 0) advanceTime(2 * 60 * 1000); // 2 minutes

        expect(context(actor).nextEventIn).not.toBeNull();

        actor.send({ type: 'next' });

        // Check active product for next refill
        if (i < defaultInput.refills - 1) {
          expect(context(actor).activeProduct).toEqual(
            expect.objectContaining({
              id: `price${i + 2}`,
              price: 10000 + (i + 1) * 5000,
              name: `Product ${i + 2}`,
            }),
          );
        }
      }

      // Check final context
      expect(context(actor)).toMatchObject({
        currentRefill: context(actor).refills,
        completedAt: expect.any(String),
      });
      expect(state(actor)).toBe('completed');
    });

    it('should emit the expected notification events', () => {
      for (let i = 0; i <= context(actor).refills; i++) {
        if (i === 0) advanceTime(2 * 60 * 1000);
        actor.send({ type: 'next' });
        actor.send({ type: 'chargeSucceeded' });
        actor.send({ type: 'prescribed' });

        const nextRefillIn = dayjs(context(actor).nextRefillDate)
          .subtract(daysLeftForNotification.nextRefill, 'days')
          .toISOString();

        expect(context(actor).nextNotificationIn).toBe(nextRefillIn);
        expect(context(actor).notificationSent.nextRefill).toBe(false);
        expect(context(actor).notificationSent.nextDose).toBe(false);
        advanceTimeInDays(21);
        if (i > 0) advanceTime(2 * 60 * 1000); // 2 minutes

        const nextDoseIn = dayjs(context(actor).nextRefillDate)
          .subtract(daysLeftForNotification.nextDose, 'days')
          .toISOString();

        actor.send({ type: 'notify' });
        expect(context(actor).nextNotificationIn).toBe(nextDoseIn);
        expect(context(actor).notificationSent.nextRefill).toBe(true);
        expect(context(actor).notificationSent.nextDose).toBe(false);
        advanceTimeInDays(5);
        actor.send({ type: 'notify' });
        expect(context(actor).nextNotificationIn).toBeNull();
        expect(context(actor).notificationSent.nextRefill).toBe(true);
        expect(context(actor).notificationSent.nextDose).toBe(true);
        advanceTimeInDays(2);
        actor.send({ type: 'next' });
      }
    });
  });

  describe('Given a Treatment with Delay', () => {
    let delayedInput: TreatmentMachineInput;

    beforeEach(() => {
      delayedInput = {
        ...createDefaultInput(3),
        delayUntil: '2023-01-05T00:00:00Z', // Start the treatment 5 days later
      };

      machine = createTreatmentMachine((e) => {});
      actor = createActor(machine, { input: delayedInput });
      actor.start();
    });

    it('should handle delayed treatments correctly', () => {
      // Initially, the machine should be in 'scheduled' state
      expect(state(actor)).toEqual('scheduled');

      // Try to proceed to 'inProgress' before delayUntil
      actor.send({ type: 'next' });
      expect(state(actor)).toEqual('scheduled');

      // Advance time to after delayUntil
      advanceTimeInDays(5);

      // Now send 'next', should proceed to 'inProgress'
      actor.send({ type: 'next' });
      expect(state(actor)).toEqual({
        inProgress: 'readyToCharge',
      });

      // Now proceed with the rest similar to 'Happy path'
      for (let i = 0; i <= delayedInput.refills; i++) {
        if (i === 0) advanceTime(2 * 60 * 1000); // 2 minutes

        actor.send({ type: 'next' });
        expect(state(actor)).toEqual({ inProgress: 'charging' });
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'treatment.prescriptionCreate',
          expect.any(Object),
        );

        // Simulate successful charge
        actor.send({ type: 'chargeSucceeded' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingForPrescription',
        });

        // Simulate prescription
        actor.send({ type: 'prescribed' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingBetweenRefills',
        });

        advanceTimeInDays(28);
        if (i > 0) advanceTime(2 * 60 * 1000); // 2 minutes

        actor.send({ type: 'next' });
      }

      // Final prescription should complete the treatment
      expect(state(actor)).toBe('completed');
      expect(context(actor).completedAt).not.toBeNull();
    });

    it('should set correct context values for delayed treatment', () => {
      const delayedInput: TreatmentMachineInput = {
        ...createDefaultInput(3),
        delayUntil: '2023-01-05T00:00:00Z',
      };
      machine = createTreatmentMachine((e) => {});
      actor = createActor(machine, { input: delayedInput });
      actor.start();

      // Initially, the machine should be in 'scheduled' state
      expect(state(actor)).toEqual('scheduled');

      // Try to proceed to 'inProgress' before delayUntil
      actor.send({ type: 'next' });
      expect(state(actor)).toEqual('scheduled');
      expect(context(actor).inProgressSince).toBeNull();
      expect(context(actor).nextEventIn).not.toBeNull();

      // Advance time to after delayUntil
      advanceTimeInDays(5);
      // Now send 'next', should proceed to 'inProgress'
      actor.send({ type: 'next' });

      // Now proceed with the rest of the refills
      for (let i = 0; i <= delayedInput.refills; i++) {
        if (i === 0) advanceTime(2 * 60 * 1000); // 2 minutes delay for first prescription

        expect(state(actor)).toEqual({
          inProgress: 'readyToCharge',
        });
        actor.send({ type: 'next' });
        expect(state(actor)).toEqual({ inProgress: 'charging' });
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'treatment.prescriptionCreate',
          expect.any(Object),
        );

        // Simulate successful charge
        actor.send({ type: 'chargeSucceeded' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingForPrescription',
        });

        // Simulate prescription
        actor.send({ type: 'prescribed' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingBetweenRefills',
        });

        // Check context after refill
        expect(context(actor).lastChargedAt).not.toBeNull();
        expect(context(actor).currentRefill).toBe(i);
        expect(omit(['startDate'], context(actor).activeProduct)).toMatchObject(
          omit(['startDate'], delayedInput.productPricesList[i]),
        );

        advanceTimeInDays(28);
        if (i > 0) advanceTime(2 * 60 * 1000); // 2 minutes

        actor.send({ type: 'next' });
      }

      // Final prescription should complete the treatment
      expect(state(actor)).toBe('completed');
      expect(context(actor).completedAt).not.toBeNull();
    });

    it('should immediately start after resuming a previously delayed treatment', () => {
      // Set up the initial delayed state
      const delayedInput: TreatmentMachineInput = {
        ...defaultInput,
        delayUntil: '2023-01-05T00:00:00Z', // Start the treatment 5 days later
      };

      const machine = createTreatmentMachine((e) => {});
      const actor = createActor(machine, { input: delayedInput });
      actor.start();

      // Check initial state
      expect(state(actor)).toEqual('scheduled');

      // Advance time, but not past delayUntil
      advanceTime(2 * 24 * 60 * 60 * 1000);

      actor.send({ type: 'next' });
      expect(state(actor)).toEqual('scheduled');

      // Pause the treatment
      actor.send({ type: 'pause' });
      expect(state(actor)).toEqual('paused');

      // Advance time past the original delay
      advanceTime(1 * 24 * 60 * 60 * 1000);

      // Resume the treatment
      actor.send({ type: 'resume' });

      // Check that it immediately goes to 'inProgress' state
      expect(state(actor)).toEqual({
        inProgress: 'readyToCharge',
      });

      // Verify that the context has been updated correctly
      expect(context(actor).inProgressSince).not.toBeNull();
      expect(context(actor).pausedAt).toBeNull();
    });
  });

  describe('Given a Short Initial Treatment', () => {
    let shortPrescriptionInput: TreatmentMachineInput;

    beforeEach(() => {
      shortPrescriptionInput = {
        ...createDefaultInput(3),
      };
      shortPrescriptionInput.productPricesList[0].days = 21; // Short initial prescription

      machine = createTreatmentMachine((e) => {});
      actor = createActor(machine, { input: shortPrescriptionInput });
      actor.start();
    });

    it('should handle a short initial prescription correctly', () => {
      for (let i = 0; i <= shortPrescriptionInput.refills; i++) {
        expect(state(actor)).toEqual({
          inProgress: 'readyToCharge',
        });

        if (i === 0) advanceTime(2 * 60 * 1000);

        actor.send({ type: 'next' });
        expect(state(actor)).toEqual({ inProgress: 'charging' });
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'treatment.prescriptionCreate',
          expect.any(Object),
        );

        // Simulate successful charge
        actor.send({ type: 'chargeSucceeded' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingForPrescription',
        });

        // Simulate prescription
        actor.send({ type: 'prescribed' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingBetweenRefills',
        });

        const daysToAdvance = i === 0 ? 21 : 28; // Short initial prescription
        advanceTimeInDays(daysToAdvance);
        if (i > 0) advanceTime(2 * 60 * 1000); // 2 minutes

        actor.send({ type: 'next' });
      }

      // Final prescription should complete the treatment
      expect(state(actor)).toBe('completed');
      expect(context(actor).completedAt).not.toBeNull();
    });

    it('should set correct context values for short initial prescription', () => {
      // Check initial context
      expect(context(actor)).toMatchObject({
        patientId: 'patient1',
        treatmentId: 'treatment1',
        currentRefill: 0,
        refills: defaultInput.refills,
        inProgressSince: mockNow,
        lastChargedAt: null,
        pausedAt: null,
      });

      for (let i = 0; i <= defaultInput.refills; i++) {
        if (i === 0) advanceTime(2 * 60 * 1000); // 2 minutes delay for first prescription

        expect(state(actor)).toEqual({
          inProgress: 'readyToCharge',
        });
        actor.send({ type: 'next' });
        expect(state(actor)).toEqual({ inProgress: 'charging' });
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'treatment.prescriptionCreate',
          expect.any(Object),
        );

        // Simulate successful charge
        actor.send({ type: 'chargeSucceeded' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingForPrescription',
        });

        // Simulate prescription
        actor.send({ type: 'prescribed' });
        expect(state(actor)).toEqual({
          inProgress: 'waitingBetweenRefills',
        });

        // Check context after refill
        expect(context(actor).lastChargedAt).not.toBeNull();
        expect(context(actor).currentRefill).toBe(i);
        expect(context(actor).activeProduct).toMatchObject(
          shortPrescriptionInput.productPricesList[i],
        );

        // Advance time based on the prescription duration
        const days = i === 0 ? 21 : 28;
        advanceTimeInDays(days);
        if (i > 0) advanceTime(2 * 60 * 1000); // 2 minutes

        actor.send({ type: 'next' });
      }

      // Final prescription should complete the treatment
      expect(state(actor)).toBe('completed');
      expect(context(actor).completedAt).not.toBeNull();
    });
  });

  describe('Given the Treatment is Paused', () => {
    let simplePrescriptionInput: TreatmentMachineInput;

    beforeEach(() => {
      simplePrescriptionInput = {
        ...createDefaultInput(3),
        delayUntil: dayjs(mockNow).add(5, 'day').toISOString(),
      };

      machine = createTreatmentMachine((e) => {});
      actor = createActor(machine, { input: simplePrescriptionInput });
      actor.start();
    });

    it('should handle pause during scheduled state', () => {
      expect(state(actor)).toBe('scheduled');

      // Pause during scheduled state
      actor.send({ type: 'pause' });
      expect(state(actor)).toEqual('paused');

      // Advance time during pause
      advanceTime(3 * 24 * 60 * 60 * 1000); // 3 days

      // Resume treatment
      actor.send({ type: 'resume' });

      // Should immediately start the treatment
      expect(state(actor)).toEqual({
        inProgress: 'readyToCharge',
      });
    });

    it('should handle pause during refill and account for pause time', () => {
      // Start treatment and complete first refill
      advanceTimeInDays(5);
      advanceTime(1000);
      actor.send({ type: 'next' });
      advanceTime(2 * 60 * 1000);
      actor.send({ type: 'next' });
      actor.send({ type: 'chargeSucceeded' });
      actor.send({ type: 'prescribed' });

      expect(state(actor)).toEqual({
        inProgress: 'waitingBetweenRefills',
      });

      // Pause during refill
      advanceTimeInDays(10);
      actor.send({ type: 'next' });
      expect(state(actor)).toEqual({
        inProgress: 'waitingBetweenRefills',
      });

      expect(context(actor).nextNotificationIn).not.toBeNull();

      actor.send({ type: 'pause' });
      expect(state(actor)).toEqual('paused');
      expect(context(actor).nextNotificationIn).toBeNull();

      // Advance time during pause
      advanceTimeInDays(5);
      actor.send({ type: 'next' });
      const paused = context(actor);
      expect(state(actor)).toEqual('paused');

      // Resume treatment
      actor.send({ type: 'resume' });
      expect(state(actor)).toEqual({
        inProgress: 'waitingBetweenRefills',
      });
      expect(context(actor).nextRefillDate).toBe(
        dayjs(paused.nextRefillDate).add(5, 'day').toISOString(),
      );
      expect(context(actor).endOfLastRefillDate).toBe(
        dayjs(paused.endOfLastRefillDate).add(5, 'day').toISOString(),
      );
      expect(context(actor).nextNotificationIn).not.toBeNull();
      // expect(context(actor).nextNotificationIn).toBe(
      //   dayjs(nextNotificationIn).add(5, 'day').toISOString(),
      // );

      // Advance time should have completed the first refill if not paused
      advanceTimeInDays(13);
      actor.send({ type: 'next' });
      expect(state(actor)).toEqual({
        inProgress: 'waitingBetweenRefills',
      });

      // Advance time to complete refill (28 + 5 - 10 - 18 = 5 days)
      advanceTimeInDays(5);
      actor.send({ type: 'next' });

      // Should now be ready for next refill
      expect(state(actor)).toEqual({
        inProgress: 'readyToCharge',
      });
      expect(context(actor).currentRefill).toBe(1);
    });

    it('should handle multiple pauses within a single refill', () => {
      // Start treatment and complete first refill
      advanceTime(5 * 24 * 60 * 60 * 1000 + 1);
      actor.send({ type: 'next' });
      advanceTime(2 * 60 * 1000);
      actor.send({ type: 'next' });
      actor.send({ type: 'chargeSucceeded' });
      actor.send({ type: 'prescribed' });

      advanceTimeInDays(10);

      // First pause
      actor.send({ type: 'pause' });
      advanceTimeInDays(1);
      actor.send({ type: 'resume' });

      advanceTimeInDays(10);

      // Second pause
      actor.send({ type: 'pause' });
      advanceTimeInDays(1);
      actor.send({ type: 'resume' });

      // not enough time to complete refill
      advanceTimeInDays(6);
      actor.send({ type: 'next' });

      // Should still be ready for next refill
      expect(state(actor)).toEqual({
        inProgress: 'waitingBetweenRefills',
      });

      // Advance the total paused time
      advanceTimeInDays(2);
      actor.send({ type: 'next' });

      // Should now be ready for next refill
      expect(state(actor)).toEqual({
        inProgress: 'readyToCharge',
      });
      expect(context(actor).currentRefill).toBe(1);
    });
  });

  describe('Given Treatment is Cancelled', () => {
    describe('While Scheduled', () => {
      beforeEach(() => {
        const input = {
          ...createDefaultInput(3),
          delayUntil: '2023-01-05T00:00:00Z',
        };
        machine = createTreatmentMachine((e) => {});
        actor = createActor(machine, { input });
        actor.start();
      });

      it('should emit delete event when cancelled in scheduled state', () => {
        expect(state(actor)).toBe('scheduled');

        actor.send({ type: 'cancel' });

        expect(state(actor)).toBe('cancelled');
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'treatment.cancelled',
          expect.any(Object),
        );
      });

      it('should set correct context values when cancelled in scheduled state', () => {
        const initialContext = context(actor);
        expect(initialContext.delayUntil).not.toBeNull();
        expect(initialContext.cancelledAt).toBeNull();

        actor.send({ type: 'cancel' });

        const finalContext = context(actor);
        expect(finalContext.cancelledAt).not.toBeNull(); // Should still be null as we're emitting delete event
        expect(context(actor).nextEventIn).toBeNull();
      });
    });

    describe('While Waiting for Charging in First Payment', () => {
      beforeEach(() => {
        machine = createTreatmentMachine((e) => {});
        actor = createActor(machine, { input: defaultInput });
        actor.start();
      });

      it('should emit delete event when cancelled during first refill', () => {
        expect(state(actor)).toEqual({
          inProgress: 'readyToCharge',
        });
        expect(context(actor).currentRefill).toBe(0);

        actor.send({ type: 'cancel' });

        expect(state(actor)).toBe('cancelled');
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'treatment.delete',
          expect.any(Object),
        );
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'treatment.cancelled',
          expect.any(Object),
        );
      });

      it('should set correct context values when cancelled during first refill', () => {
        const initialContext = context(actor);
        expect(initialContext.inProgressSince).not.toBeNull();
        expect(initialContext.cancelledAt).toBeNull();

        actor.send({ type: 'cancel' });

        const finalContext = context(actor);
        expect(finalContext.cancelledAt).toBeNull(); // Should still be null as we're emitting delete event
        expect(context(actor).nextEventIn).toBeNull();
      });
    });

    describe('While During the Treatment', () => {
      beforeEach(() => {
        machine = createTreatmentMachine((e) => {});
        actor = createActor(machine, { input: defaultInput });
        actor.start();
      });

      it('should handle cancellation correctly after first refill', () => {
        // Simulate progression to second refill
        advanceTime(2 * 60 * 1000); // 2 minutes
        actor.send({ type: 'next' });
        actor.send({ type: 'chargeSucceeded' });
        actor.send({ type: 'prescribed' });
        advanceTimeInDays(28);
        actor.send({ type: 'next' });

        expect(context(actor).currentRefill).toBe(1);

        actor.send({ type: 'cancel' });

        expect(state(actor)).toBe('cancelled');
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'treatment.cancelled',
          expect.any(Object),
        );
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          'treatment.delete',
          expect.any(Object),
        );
      });

      it('should set correct context values when cancelled after first refill', () => {
        // Simulate progression to second refill
        advanceTime(2 * 60 * 1000); // 2 minutes
        actor.send({ type: 'next' });
        actor.send({ type: 'chargeSucceeded' });
        actor.send({ type: 'prescribed' });
        advanceTime(28 * 24 * 60 * 60 * 1000); // 28 days
        actor.send({ type: 'next' });

        const contextBeforeCancel = context(actor);
        expect(contextBeforeCancel.currentRefill).toBe(1);
        expect(contextBeforeCancel.inProgressSince).not.toBeNull();
        expect(contextBeforeCancel.lastChargedAt).not.toBeNull();
        expect(contextBeforeCancel.cancelledAt).toBeNull();

        actor.send({ type: 'cancel' });

        const finalContext = context(actor);
        expect(finalContext.cancelledAt).not.toBeNull();
        expect(finalContext.cancelledAt).toBe(mockNow);
        expect(finalContext).toEqual({
          ...contextBeforeCancel,
          cancelledAt: mockNow,
          nextEventIn: null,
          nextNotificationIn: null,
        });
      });
    });
  });

  describe('Given Payment is Marked uncollectible', () => {
    beforeEach(() => {
      machine = createTreatmentMachine((e) => {});
      actor = createActor(machine, { input: createDefaultInput(3) });
      actor.start();

      // Advance time to be ready to charge
      advanceTime(2 * 60 * 1000); // 2 minutes
      actor.send({ type: 'next' });
    });

    it('should handle uncollectible payments correctly', () => {
      // Simulate charge failure
      actor.send({ type: 'chargeFailed' });
      expect(state(actor)).toBe('failed');

      // Mark as uncollectible
      actor.send({ type: 'uncollectible' });
      expect(state(actor)).toBe('uncollectible');
    });

    it('should set correct context values for uncollectible treatment', () => {
      // Check initial context
      const initialContext = context(actor);
      expect(initialContext.failedAt).toBeNull();
      expect(initialContext.uncollectibleAt).toBeNull();

      // Simulate charge failure
      actor.send({ type: 'chargeFailed' });

      // Check context after failure
      const failedContext = context(actor);
      expect(failedContext.failedAt).not.toBeNull();
      expect(failedContext.failedAt).toBe(mockNow);
      expect(failedContext.uncollectibleAt).toBeNull();

      // Advance time before marking as uncollectible
      const uncollectibleTime = '2023-01-02T00:00:00.000Z';
      mockNow = uncollectibleTime;

      // Mark as uncollectible
      actor.send({ type: 'uncollectible' });

      // Check final context
      const finalContext = context(actor);
      expect(finalContext.uncollectibleAt).not.toBeNull();
      expect(finalContext.uncollectibleAt).toBe(uncollectibleTime);

      // For the moment we'll omit end dates on this cases
      const compareContext = {
        ...omit(['products'], finalContext),
        products: finalContext.products.map((p) => omit(['endDate'], p)),
      };
      const compareFailedContext = {
        ...omit(['products'], failedContext),
        products: failedContext.products.map((p) => omit(['endDate'], p)),
      };
      expect(compareContext).toEqual({
        ...compareFailedContext,
        uncollectibleAt: uncollectibleTime,
        failedAt: null,
        nextEventIn: null,
      });
    });
  });

  describe('Given a Treatment with Date changes Triggered', () => {
    beforeEach(() => {
      machine = createTreatmentMachine((e) => {});
      actor = createActor(machine, { input: createDefaultInput(3) });
      actor.start();

      // Advance time to be ready to charge
      advanceTime(2 * 60 * 1000); // 2 minutes
      actor.send({ type: 'next' });
    });

    it('should trigger next states immediately', () => {
      actor.send({ type: 'chargeSucceeded' });
      actor.send({ type: 'prescribed' });
      expect(state(actor)).toEqual({ inProgress: 'waitingBetweenRefills' });
      actor.send({ type: 'fireNext' });
      expect(state(actor)).toEqual({ inProgress: 'readyToCharge' });
      expect(context(actor).currentRefill).toBe(1);
    });

    it('should move dates accordingly when firing next refill immediately', () => {
      actor.send({ type: 'chargeSucceeded' });
      actor.send({ type: 'prescribed' });
      const nextRefillDate = context(actor).nextRefillDate;
      const endOfLastRefillDate = context(actor).endOfLastRefillDate;
      actor.send({ type: 'fireNext' });
      expect(context(actor).nextRefillDate).toBe(
        dayjs(mockNow)
          .add(context(actor).activeProduct.days, 'days')
          .toISOString(),
      );
      expect(context(actor).endOfLastRefillDate).toBe(
        dayjs(endOfLastRefillDate)
          .subtract(dayjs(nextRefillDate).diff(dayjs(mockNow)))
          .toISOString(),
      );
    });

    it('should move dates accordingly when moving next refill date', () => {
      actor.send({ type: 'chargeSucceeded' });
      actor.send({ type: 'prescribed' });
      const nextRefillDate = context(actor).nextRefillDate;
      const endOfLastRefillDate = context(actor).endOfLastRefillDate;
      const newRefillDate = '2023-02-01T00:00:00.000Z';
      actor.send({ type: 'moveRefillDate', date: newRefillDate });
      expect(context(actor).nextRefillDate).toBe(newRefillDate);
      expect(context(actor).endOfLastRefillDate).toBe(
        dayjs(endOfLastRefillDate)
          .add(dayjs(newRefillDate).diff(dayjs(nextRefillDate)))
          .toISOString(),
      );
    });

    it('should not allow you to move to a date in the past', () => {
      actor.send({ type: 'chargeSucceeded' });
      actor.send({ type: 'prescribed' });
      const nextRefillDate = context(actor).nextRefillDate;
      const endOfLastRefillDate = context(actor).endOfLastRefillDate;
      const newRefillDate = '2022-12-01T00:00:00.000Z';
      actor.send({ type: 'moveRefillDate', date: newRefillDate });
      expect(context(actor).nextRefillDate).toBe(nextRefillDate);
      expect(context(actor).endOfLastRefillDate).toBe(endOfLastRefillDate);
    });

    it('should trigger notifications on sooner refill changes', () => {
      actor.send({ type: 'chargeSucceeded' });
      actor.send({ type: 'prescribed' });
      const nextNotificationIn = context(actor).nextNotificationIn;
      expect(context(actor).nextNotificationIn).toBe(
        dayjs(context(actor).nextRefillDate)
          .subtract(daysLeftForNotification.nextRefill, 'days')
          .toISOString(),
      );
      const subtract = 5;
      const newRefillDate = dayjs(context(actor).nextRefillDate)
        .subtract(subtract, 'days')
        .toISOString();
      actor.send({ type: 'moveRefillDate', date: newRefillDate });
      expect(context(actor).nextNotificationIn).toBe(
        dayjs(context(actor).nextRefillDate)
          .subtract(daysLeftForNotification.nextRefill, 'days')
          .toISOString(),
      );
      expect(context(actor).nextNotificationIn).toBe(
        dayjs(nextNotificationIn).subtract(subtract, 'days').toISOString(),
      );
      actor.send({ type: 'notify' });
      expect(eventEmitter.emit).not.toHaveBeenCalledWith(
        'treatment.notify.nextRefill',
        expect.any(Object),
      );
      advanceTimeInDays(context(actor).activeProduct.days - 7 - subtract);
      actor.send({ type: 'notify' });
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        'treatment.notify.nextRefill',
        expect.any(Object),
      );
    });

    it('should trigger notifications on later refill changes', () => {
      actor.send({ type: 'chargeSucceeded' });
      actor.send({ type: 'prescribed' });
      const nextNotificationIn = context(actor).nextNotificationIn;
      expect(context(actor).nextNotificationIn).toBe(
        dayjs(context(actor).nextRefillDate)
          .subtract(daysLeftForNotification.nextRefill, 'days')
          .toISOString(),
      );
      const add = 5;
      const newRefillDate = dayjs(context(actor).nextRefillDate)
        .add(add, 'days')
        .toISOString();
      actor.send({ type: 'moveRefillDate', date: newRefillDate });
      expect(context(actor).nextNotificationIn).toBe(
        dayjs(context(actor).nextRefillDate)
          .subtract(daysLeftForNotification.nextRefill, 'days')
          .toISOString(),
      );
      expect(context(actor).nextNotificationIn).toBe(
        dayjs(nextNotificationIn).add(add, 'days').toISOString(),
      );
      actor.send({ type: 'notify' });
      expect(eventEmitter.emit).not.toHaveBeenCalledWith(
        'treatment.notify.nextRefill',
        expect.any(Object),
      );
      advanceTimeInDays(context(actor).activeProduct.days - 7 + add);
      actor.send({ type: 'notify' });
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        'treatment.notify.nextRefill',
        expect.any(Object),
      );
    });
  });

  describe('Given Failed Payments', () => {
    beforeEach(() => {
      machine = createTreatmentMachine((e) => {});
      actor = createActor(machine, { input: createDefaultInput(3) });
      actor.start();

      // Advance time to be ready to charge
      advanceTime(2 * 60 * 1000); // 2 minutes
      actor.send({ type: 'next' });

      // Simulate charge failure
      actor.send({ type: 'chargeFailed' });
    });

    it('should recover from failure and continue treatment', () => {
      expect(state(actor)).toBe('failed');

      // Attempt to retry
      actor.send({ type: 'retry', invoiceId: null });

      // Should return to charging state
      expect(state(actor)).toEqual({ inProgress: 'charging' });
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        'treatment.prescriptionCreate',
        expect.any(Object),
      );

      // Simulate successful charge
      actor.send({ type: 'chargeSucceeded' });

      // Continue the normal flow
      expect(state(actor)).toEqual({
        inProgress: 'waitingForPrescription',
      });
      actor.send({ type: 'prescribed' });
      expect(state(actor)).toEqual({
        inProgress: 'waitingBetweenRefills',
      });

      // Advance time for next refill
      advanceTimeInDays(28);
      actor.send({ type: 'next' });

      // Proceed to next refill or complete
      expect(state(actor)).toEqual({
        inProgress: 'readyToCharge',
      });
    });

    it('should set correct context values during failure and recovery', () => {
      // Check context after failure
      const failedContext = context(actor);
      expect(failedContext.failedAt).not.toBeNull();
      expect(failedContext.failedAt).toBe(mockNow);
      expect(context(actor).nextEventIn).toBeNull();

      // Advance time before recovery
      const recoveryTime = '2023-01-02T00:00:00.000Z';
      mockNow = recoveryTime;

      // Attempt to recover
      actor.send({ type: 'retry', invoiceId: null });

      // Check context after recovery attempt
      const recoveryContext = context(actor);
      expect(recoveryContext.failedAt).toBeNull();

      // Simulate successful charge
      actor.send({ type: 'chargeSucceeded' });

      // Check context after successful charge
      const chargedContext = context(actor);
      expect(chargedContext.lastChargedAt).toBe(recoveryTime);

      // Continue the normal flow
      actor.send({ type: 'prescribed' });

      // Advance time for next refill
      mockNow = '2023-01-30T00:00:00.000Z';
      actor.send({ type: 'next' });

      // Check final context
      const finalContext = context(actor);
      expect(finalContext.currentRefill).toBe(1);
      expect(finalContext.lastChargedAt).toBe(recoveryTime);
      expect(finalContext.failedAt).toBeNull();
      expect(finalContext.inProgressSince).toBe(failedContext.inProgressSince);
      expect(context(actor).nextEventIn).not.toBeNull(); // because it prescribed and its waiting for refill
    });
  });

  describe('Given the Treatment is Persisted and then Restored', () => {
    beforeEach(() => {
      machine = createTreatmentMachine((e) => {});
      actor = createActor(machine, { input: createDefaultInput(3) });
      actor.start();
    });

    it('should resume from a paused state correctly', () => {
      // Start treatment and complete first refill
      advanceTime(2 * 60 * 1000);
      actor.send({ type: 'next' });
      actor.send({ type: 'chargeSucceeded' });
      actor.send({ type: 'prescribed' });

      expect(state(actor)).toEqual({
        inProgress: 'waitingBetweenRefills',
      });

      // Pause during refill
      advanceTime(10 * 24 * 60 * 60 * 1000); // 10 days into refill
      actor.send({ type: 'pause' });
      expect(state(actor)).toEqual('paused');

      const persistedSnapshot = actor.getPersistedSnapshot();

      const restoredActor = createActor(
        createTreatmentMachine((e) => {}),
        {
          snapshot: persistedSnapshot,
        },
      ).start();

      expect(state(restoredActor)).toEqual('paused');

      // Resume treatments
      actor.send({ type: 'resume' });
      restoredActor.send({ type: 'resume' });

      expect(state(actor)).toEqual({
        inProgress: 'waitingBetweenRefills',
      });

      expect(state(restoredActor)).toEqual({
        inProgress: 'waitingBetweenRefills',
      });
    });
  });
});
