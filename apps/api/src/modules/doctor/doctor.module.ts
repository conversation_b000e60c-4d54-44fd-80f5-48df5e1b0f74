import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { QuestionnairService } from '@/modules/shared/questionnaire/questionnaire.service';
import { DoctorPersistence } from '@adapters/persistence/database/doctor.persistence';
import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { AuthModule } from '@modules/auth/auth.module';
import { AppCacheModule } from '@modules/cache/cache.module';
import { ChatModule } from '@modules/chat/chat.module';
import { DoctorAdminController } from '@modules/doctor/doctor-admin.controller';
import { DoctorAuthController } from '@modules/doctor/doctor-auth.controller';
import { DoctorAcceptPatientUseCase } from '@modules/doctor/use-cases/doctor-accept-patient.use-case';
import { DoctorGetProfilePictureUrlUseCase } from '@modules/doctor/use-cases/doctor-get-profile-picture-url.use-case';
import { Doctor<PERSON>ejectVerificationUseCase } from '@modules/doctor/use-cases/doctor-reject-verification.use-case';
import { DoctorSignInUseCase } from '@modules/doctor/use-cases/doctor-sign-in.use-case';
import { DoctorVerifyPatientUseCase } from '@modules/doctor/use-cases/doctor-verify-patient.use-case';
import { GetDashboardForDoctorUseCase } from '@modules/doctor/use-cases/get-dashboard-for-doctor.use-case';
import { GetPatientForDoctorUseCase } from '@modules/doctor/use-cases/get-patient-for-doctor.use-case';
import { ShiftDatesUseCase } from '@modules/doctor/use-cases/shift-dates.use-case';
import { DosespotModule } from '@modules/dosespot/dosespot.module';
import { PatientModule } from '@modules/patient/patient.module';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { SesModule } from '@modules/shared/aws/ses/ses.module';
import { SqsModule } from '@modules/shared/aws/sqs/sqs.module';
import { OutboxerModule } from '@modules/shared/outboxer/outboxer.module';
import { SharedModule } from '@modules/shared/shared.module';
import { StripeModule } from '@modules/stripe/stripe.module';
import { TreatmentModule } from '@modules/treatment/treatment.module';
import { forwardRef, Module } from '@nestjs/common';

import { AuditLogModule } from '../audit-log/audit-log.module';
import { PatientCancelSubscriptionUseCase } from '../patient/use-cases/patient-cancel-subscription.use-case';
import { DoctorController } from './doctor.controller';
import { DoctorService } from './doctor.service';
import { DoctorGetByIdUseCase } from './use-cases/doctor-get-by-id.use-case';
import { DoctorIntakeSwimlaneUseCase } from './use-cases/doctor-intake-swimlane-use-case.service';
import { DoctorRefreshTokenUseCase } from './use-cases/doctor-refresh-token-use.case';
import { GetDoctorsForDoctorUseCase } from './use-cases/get-doctors-for-doctor.use-case';
import { GetPatientActivityLogUseCase } from './use-cases/get-patient-activity-log.use-case';
import { GetPatientPreSignedUrlUseCase } from './use-cases/get-patient-pre-signed-url.use-case';
import { GetPatientsForDoctorUseCase } from './use-cases/get-patients-for-doctor.use-case';
import { InpatientPendingDosespotPrescriptionUseCase } from './use-cases/inpatient-pending-dosespot-prescription.use-case';
import { PatientDosageUseCase } from './use-cases/patient-dosage.use-case';
import { PatientIntakeUseCase } from './use-cases/patient-intake-use-case.service';
import { UpdatePatientIdInfoUseCase } from './use-cases/update-patient-id-info.use-case';
import { UpdatePatientPhotoUseCase } from './use-cases/update-patient-photo.use-case';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    forwardRef(() => SharedModule),
    AppCacheModule,
    DosespotModule,
    forwardRef(() => PatientModule), // Important: Using forwardRef for circular dependency
    forwardRef(() => ChatModule),
    PrismaModule,
    StripeModule,
    AuditLogModule,
    OutboxerModule,
    SqsModule,
    SesModule,
    forwardRef(() => TreatmentModule),
  ],
  controllers: [DoctorAuthController, DoctorController, DoctorAdminController],
  providers: [
    DoctorSignInUseCase,
    DoctorAcceptPatientUseCase,
    DoctorVerifyPatientUseCase,
    DoctorRefreshTokenUseCase,
    DoctorSignInUseCase,
    PatientDosageUseCase,
    DoctorIntakeSwimlaneUseCase,
    PatientIntakeUseCase,
    InpatientPendingDosespotPrescriptionUseCase,
    QuestionnairService,
    DoctorGetByIdUseCase,
    GetPatientsForDoctorUseCase,
    GetDoctorsForDoctorUseCase,
    DoctorPersistence,
    PatientPersistence,
    DoctorService,
    GetPatientForDoctorUseCase,
    DoctorRejectVerificationUseCase,
    GetDashboardForDoctorUseCase,
    PatientCancelSubscriptionUseCase,
    ShiftDatesUseCase,
    PatientPaymentMethodPersistence,
    UpdatePatientIdInfoUseCase,
    GetPatientPreSignedUrlUseCase,
    UpdatePatientPhotoUseCase,
    DoctorGetProfilePictureUrlUseCase,
    GetPatientActivityLogUseCase,
  ],
  exports: [
    DoctorService,
    GetPatientPreSignedUrlUseCase,
    UpdatePatientPhotoUseCase,
  ],
})
export class DoctorModule {}
