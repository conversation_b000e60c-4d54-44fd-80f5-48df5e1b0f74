import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { format, toZonedTime } from 'date-fns-tz';

@Injectable()
export class GoogleSheetsService {
  private readonly logger = new Logger(GoogleSheetsService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Append a patient message analysis record to the Google Sheet
   * @param data The analysis data to record
   * @returns Success status
   */
  async appendAnalysisRecord(data: {
    originalMessages: string;
    originalSummary: string;
    originalRelevantForPS: boolean;
    originalRelevantForDoctor: boolean;
    date: string;
    [key: string]: any; // Allow dynamic fields for model results
  }): Promise<{ success: boolean }> {
    try {
      // Get the current environment
      const environment =
        this.configService.get<string>('ENVIRONMENT') || 'unknown';

      // Format the data for the request - spread all data fields and add environment
      const formattedData = {
        ...data,
        environment: environment,
      };

      return this.appendToPublicSheet(
        formattedData,
        'https://script.google.com/macros/s/AKfycbwsdJMLc0Ia67ldatOmwoM9PpNziJg3g7HTGKwWuR485xoRmsH5Cl6o-eA59RE8S_8whQ/exec',
      );
    } catch (error) {
      this.logger.error(
        `Failed to format analysis record: ${error.message}`,
        error.stack,
      );
      return { success: false };
    }
  }

  /**
   * Append a refund record to the Google Sheet
   * @param data The refund data to record
   * @returns Success status
   */
  async appendRefundRecord(data: {
    userId: string;
    email: string;
    amount: number;
    invoiceDate: Date;
    refundDate: Date;
    invoiceId: string;
  }): Promise<{ success: boolean }> {
    try {
      // Get the current environment
      const environment =
        this.configService.get<string>('ENVIRONMENT') || 'unknown';

      // Convert UTC dates to Eastern Time and format them
      const timeZone = 'America/New_York';
      const dateFormat = 'MM/dd/yyyy h:mm:ss'; // US format with timezone indicator

      const easternInvoiceDate = toZonedTime(data.invoiceDate, timeZone);
      const easternRefundDate = toZonedTime(data.refundDate, timeZone);

      const formattedInvoiceDate = format(easternInvoiceDate, dateFormat, {
        timeZone,
      });
      const formattedRefundDate = format(easternRefundDate, dateFormat, {
        timeZone,
      });

      // Format the data for the request
      const formattedData = {
        userId: data.userId,
        email: data.email,
        amount: data.amount / 100, // Convert cents to dollars
        invoiceDate: formattedInvoiceDate,
        refundDate: formattedRefundDate,
        invoiceId: data.invoiceId,
        environment: environment,
      };

      return this.appendToPublicSheet(formattedData);
    } catch (error) {
      this.logger.error(
        `Failed to format refund record: ${error.message}`,
        error.stack,
      );
      return { success: false };
    }
  }

  /**
   * Append data to a Google Sheet using Google Apps Script
   * @param data The data to append to the sheet
   * @param scriptUrl The Google Apps Script URL
   * @returns Success status
   */
  private async appendToPublicSheet(
    data: Record<string, any>,
    scriptUrl?: string,
  ): Promise<{ success: boolean }> {
    try {
      // Use provided URL or get from config
      const url =
        scriptUrl ||
        this.configService.get<string>('GOOGLE_SCRIPT_REFUNDS_URL');
      if (!url) {
        this.logger.warn(
          'Google Script Web App URL not configured, logging data instead',
        );
        this.logger.log('Data that would be sent to Google Sheets:');
        console.log('------- DATA -------');
        console.log(JSON.stringify(data, null, 2));
        console.log('-------------------');
        return { success: true };
      }

      this.logger.log(
        `Appending data to Google Sheet via Apps Script: ${JSON.stringify(data)}`,
      );

      // Send the data directly to the Google Apps Script web app
      const response = await axios.post(url, data);

      if (response.status === 200) {
        this.logger.log('Successfully added data to Google Sheet');
        return { success: true };
      } else {
        throw new Error(
          `Unexpected response from Google Apps Script: ${JSON.stringify(response.data)}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to append data to Google Sheet: ${error.message}`,
        error.stack,
      );
      return { success: false };
    }
  }
}
