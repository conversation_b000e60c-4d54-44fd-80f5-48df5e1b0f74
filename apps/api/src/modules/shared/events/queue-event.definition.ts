import { BaseMessage } from './base.definition';
import { FollowUpUpdatedQueueEvent } from './follow-up-topic.definition';
import { StripeInvoiceUpdatedQueueEvent } from './invoice-topic.definition';
import { PatientUpdatedQueueEvent } from './patient-topic.definition';
import {
  StripeChargeUpdatedQueueEvent,
  StripeCustomerUpdatedQueueEvent,
} from './stripe-topic.definition';
import { SubscriptionUpdatedQueueEvent } from './subscription-topic.definition';
import { TreatmentUpdatedQueueEvent } from './treatment-topic.definition';

export type QueueEvent =
  | PatientUpdatedQueueEvent
  | TreatmentUpdatedQueueEvent
  | FollowUpUpdatedQueueEvent
  | StripeInvoiceUpdatedQueueEvent
  | SubscriptionUpdatedQueueEvent
  | StripeChargeUpdatedQueueEvent
  | StripeCustomerUpdatedQueueEvent;

export type QueueMessage<TTopic extends QueueEvent['metadata']['topic']> =
  BaseMessage<TTopic, Extract<QueueEvent, { _topic: TTopic }>['payload']>;

export type TopicName = QueueEvent['_topic'];
