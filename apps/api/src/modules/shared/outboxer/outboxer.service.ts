import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { Outbox, OutboxStatus, Prisma } from '@prisma/client';
import { subSeconds } from 'date-fns';

import { QueueEvent, TopicName } from '../events/queue-event.definition';

export type OutboxDestinationService = 'sqs';

export type OutboxDestination = { service: 'sqs' | 'sns'; topic?: string };

export type OutboxPayload = {
  status?: OutboxStatus;
  patientId: string;
  destination: OutboxDestination;
  payload: Record<string, any>;
  maxRetries?: number;
  deduplicationId?: string;
};

export type OutboxError = {
  message: string;
  code: string;
  details?: Record<string, any>;
};

@Injectable()
export class OutboxerService {
  private readonly logger = new Logger(OutboxerService.name);

  constructor(private readonly prisma: PrismaService) {}

  private parseDestination(destination: OutboxDestination | string): {
    service: string;
    topic?: string;
  } {
    if (typeof destination === 'string') {
      return { service: destination };
    }

    return { service: destination.service, topic: destination.topic };
  }

  private async append(
    data: OutboxPayload,
    { prisma }: { prisma?: Prisma.TransactionClient } = {},
  ): Promise<Outbox> {
    try {
      const { service, topic } = this.parseDestination(data.destination);
      const db = prisma || this.prisma;

      const outboxEntry = await db.outbox.create({
        data: {
          patientId: data.patientId,
          destination: service,
          topic: topic,
          payload: data.payload,
          deduplicationId: data.deduplicationId,
          maxRetries: data.maxRetries ?? 3,
          status: data.status ?? OutboxStatus.PENDING,
        },
      });

      this.logger.debug(
        `Message appended to outbox with ID: ${outboxEntry.id}`,
      );
      return outboxEntry;
    } catch (error) {
      this.logger.error('Failed to append message to outbox', error);
      throw error;
    }
  }

  public async searchRetryable({
    limit = 10,
    destination,
    topic,
  }: {
    limit: number;
    destination?: OutboxDestination['service'];
    topic?: string;
  }): Promise<Outbox[]> {
    try {
      const whereClause: Prisma.OutboxWhereInput = {
        OR: [
          { status: OutboxStatus.PENDING },
          {
            status: OutboxStatus.FAILED,
            retryCount: { lt: this.prisma.outbox.fields.maxRetries },
          },
        ],
      };

      if (destination) {
        whereClause.destination = destination;
      }

      if (topic) {
        whereClause.topic = topic;
      }

      const retryableMessages = await this.prisma.outbox.findMany({
        where: whereClause,
        orderBy: { createdAt: 'asc' },
        take: limit,
      });

      return retryableMessages;
    } catch (error) {
      this.logger.error('Failed to search for retryable messages', error);
      throw error;
    }
  }

  private async markProcessing(id: string): Promise<Outbox> {
    try {
      const updatedMessage = await this.prisma.outbox.update({
        where: { id },
        data: { status: OutboxStatus.PROCESSING },
      });

      return updatedMessage;
    } catch (error) {
      this.logger.error(`Failed to mark message ${id} as processing`, error);
      throw error;
    }
  }

  private async markDone(id: string): Promise<Outbox> {
    try {
      const updatedMessage = await this.prisma.outbox.update({
        where: { id },
        data: { status: OutboxStatus.DONE, processedAt: new Date() },
      });

      return updatedMessage;
    } catch (error) {
      this.logger.error(`Failed to mark message ${id} as delivered`, error);
      throw error;
    }
  }

  private async markFailed(
    id: string,
    error: string | Record<string, any>,
  ): Promise<Outbox> {
    try {
      const message = await this.prisma.outbox.findUnique({ where: { id } });

      if (!message) {
        throw new Error(`Message with ID ${id} not found`);
      }

      // Convert error to JSON object if it's a string
      const errorObject =
        typeof error === 'string' ? { message: error } : error;

      const updatedMessage = await this.prisma.outbox.update({
        where: { id },
        data: {
          status: OutboxStatus.FAILED,
          retryCount: { increment: 1 },
          error: errorObject,
        },
      });

      // Check if max retries reached
      if (updatedMessage.retryCount >= updatedMessage.maxRetries) {
        this.logger.warn(
          `Message ${id} has reached max retries and will not be retried anymore`,
        );
      }

      return updatedMessage;
    } catch (error) {
      this.logger.error(`Failed to mark message ${id} as failed`, error);
      throw error;
    }
  }

  async processMessage(
    processor: (message: Outbox) => Promise<void>,
    message: Outbox,
  ): Promise<Outbox | null> {
    try {
      await this.markProcessing(message.id);
      await processor(message);
      return this.markDone(message.id);
    } catch (error) {
      const errorData =
        typeof error === 'string'
          ? { message: error }
          : error.message
            ? { message: error.message, ...error }
            : error;

      await this.markFailed(message.id, errorData);
      return null;
    }
  }

  async enqueue<TTopicName extends TopicName>(
    patientId: string,
    queue: TTopicName,
    payload: Extract<QueueEvent, { _topic: TTopicName }>['payload'],
    options: {
      status?: OutboxStatus;
      deduplicationId?: string;
      deduplicationPeriodInSeconds?: number;
      prisma?: Prisma.TransactionClient;
    } = {
      prisma: this.prisma,
    },
  ): Promise<void> {
    let status: OutboxStatus = options.status;
    if (options.deduplicationId) {
      const existingMessage = await options.prisma.outbox.findFirst({
        where: {
          deduplicationId: options.deduplicationId,
          createdAt: {
            gte: subSeconds(
              new Date(),
              options.deduplicationPeriodInSeconds ?? 60,
            ),
          },
        },
      });

      if (existingMessage) {
        status = OutboxStatus.DUPLICATE;
        this.logger.warn(
          `Message with deduplicationId ${options.deduplicationId} already exists in the outbox.`,
          {
            patientId,
            topic: queue,
          },
        );
      }
    }

    await this.append(
      {
        status,
        deduplicationId: options.deduplicationId,
        patientId,
        destination: { service: 'sns', topic: queue },
        payload: {
          _topic: queue,
          metadata: {
            topic: queue,
            createAt: new Date(),
            patientId: patientId,
          },
          payload,
        },
      },
      options,
    );
  }
}
