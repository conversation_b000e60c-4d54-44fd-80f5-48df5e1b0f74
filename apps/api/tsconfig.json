{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@core/*": ["src/core/*"], "@modules/*": ["src/modules/*"], "@adapters/*": ["src/adapters/*"], "@commands/*": ["src/commands/*"], "@test/*": ["test/*"], "@willow/utils/*": ["node_modules/@willow/utils/src/*"], "@willow/auth": ["node_modules/@willow/auth/*"]}}}