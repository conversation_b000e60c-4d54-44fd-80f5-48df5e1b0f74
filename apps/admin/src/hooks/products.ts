import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

// Response type for the image upload endpoint
export interface UploadProductImageResponseDto {
  url: string;
}

export interface ProductPrice {
  id: string;
  productId: string;
  name: string;
  description: string | null;
  stripePriceId: string;
  amount: number;
  currency: string;
  interval: string;
  intervalCount: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ListedProduct {
  id: string;
  name: string;
  description: string | null;
  image?: string | null;
  isCore?: boolean;
  active: boolean;
  isAvailableInOnboarding?: boolean;
  canBeDeleted?: boolean;

  // New fields extracted from metadata
  form?: ProductForms;
  tags?: string;
  type?: ProductTypes;
  label?: string;
  onboardingLabel?: string;
  order?: number;
  notice?: string;
  supplyLength?: string;
  weightLossMultiplier?: number;
  customCard?: string;

  metadata?: Record<string, any> | null;
  pharmacy?: {
    id: string;
    name: string;
  } | null;
  productCategories?: {
    productCategory: {
      id: string;
      name: string;
      form?: string | null;
      label?: string | null;
      customCard?: string | null;
    };
  }[];
  defaultPrice?: {
    id: string;
    name: string;
    unit_amount: number;
    dosageLabel?: string;
  } | null;
  productPrice?: {
    id: string;
    name: string;
    unit_amount: number;
    active: boolean;
  }[];
  createdAt: string;
  updatedAt?: string;
}

// Product form types (injectable, oral, tablet)
export enum ProductForms {
  injectable = 'injectable',
  oral = 'oral',
  tablet = 'tablet',
}

// Product types (core, additional)
export enum ProductTypes {
  core = 'core',
  additional = 'additional',
}

export interface CreateProductDto {
  id?: string;
  name: string;
  description: string | null;
  image?: string | null;
  isCore?: boolean;
  active?: boolean;
  isAvailableInOnboarding?: boolean;
  pharmacyId?: string;
  productCategoryIds?: string[];

  // New fields extracted from metadata
  form?: ProductForms;
  tags?: string;
  type?: ProductTypes;
  label?: string;
  onboardingLabel?: string;
  order?: number;
  notice?: string;
  supplyLength?: string;
  weightLossMultiplier?: number;

  // Keep metadata for backward compatibility
  metadata?: Record<string, any> | null;
}

export interface UpdateProductDto {
  name?: string;
  description?: string | null;
  image?: string | null;
  isCore?: boolean;
  active?: boolean;
  isAvailableInOnboarding?: boolean;
  pharmacyId?: string;
  productCategoryIds?: string[];

  // New fields extracted from metadata
  form?: ProductForms;
  tags?: string;
  type?: ProductTypes;
  label?: string;
  onboardingLabel?: string;
  order?: number;
  notice?: string;
  supplyLength?: string;
  weightLossMultiplier?: number;

  // Keep metadata for backward compatibility
  metadata?: Record<string, any> | null;
}

interface FetchParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  direction?: 'asc' | 'desc';
  showInactive?: boolean;
  productCategoryId?: string;
}

interface ProductsResponse {
  products: ListedProduct[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Get products with pagination and search
export const useGetProducts = (params: FetchParams) => {
  return useQuery<ProductsResponse>({
    queryKey: ['products', params],
    queryFn: async () => {
      const { data } = await apiClient.get('/product', {
        params,
      });
      return data;
    },
  });
};

// Get a single product by ID
export const useGetProduct = (id: string) => {
  return useQuery<ListedProduct>({
    queryKey: ['product', id],
    queryFn: async () => {
      const { data } = await apiClient.get(`/product/${id}`);
      return data;
    },
    enabled: !!id,
  });
};

// Get products by category ID
export const useGetProductsByCategory = (
  categoryId: string,
  params?: Omit<FetchParams, 'productCategoryId'>,
) => {
  return useQuery<ProductsResponse>({
    queryKey: ['products', 'category', categoryId, params],
    queryFn: async () => {
      const { data } = await apiClient.get('/product', {
        params: {
          ...params,
          productCategoryId: categoryId,
        },
      });
      return data;
    },
    enabled: !!categoryId,
  });
};

// Create a new product
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (productData: CreateProductDto) => {
      const { data } = await apiClient.post('/product', productData);
      return data;
    },
    onSuccess: () => {
      // Invalidate the products list to update the UI with the new product
      void queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

// Update an existing product
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      productData,
    }: {
      id: string;
      productData: UpdateProductDto;
    }) => {
      const { data } = await apiClient.patch(`/product/${id}`, productData);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific product cache
      void queryClient.invalidateQueries({
        queryKey: ['product', variables.id],
      });
      // Also invalidate the products list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

// Deactivate a product
export const useDeactivateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.patch(`/product/${id}/deactivate`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific product cache
      void queryClient.invalidateQueries({ queryKey: ['product', variables] });
      // Also invalidate the products list to update the UI
      void queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

// Reactivate a product
export const useReactivateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.patch(`/product/${id}/activate`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific product cache
      void queryClient.invalidateQueries({ queryKey: ['product', variables] });
      // Also invalidate the products list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

// Upload a product image
export const useUploadProductImage = () => {
  return useMutation({
    mutationFn: async (file: File): Promise<UploadProductImageResponseDto> => {
      const formData = new FormData();
      formData.append('image', file);

      const { data } = await apiClient.post('/product/upload-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return data;
    },
  });
};

// Delete a product
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete(`/product/${id}`);
      return data;
    },
    onSuccess: (_data, id) => {
      // Invalidate the specific product cache
      void queryClient.invalidateQueries({ queryKey: ['product', id] });
      // Also invalidate the products list to update the UI
      void queryClient.invalidateQueries({ queryKey: ['products'] });
      // Invalidate any related product price queries
      void queryClient.invalidateQueries({ queryKey: ['product-prices'] });
    },
  });
};
