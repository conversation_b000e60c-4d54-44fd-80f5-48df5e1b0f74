'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { parseAsString, useQueryState } from 'nuqs';

import { cn } from '@willow/ui';
import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { ColumnHeader } from '@willow/ui/column-header';
import { Loader } from '@willow/ui/loader';
import { Pagination } from '@willow/ui/pagination';
import { SearchInput } from '@willow/ui/search-input';
import {
  usePaginationSearchParams,
  useSortingQueryState,
} from '@willow/utils/table';

import type { ProductCategory } from '~/hooks/product-categories';
import { useGetProductCategories } from '~/hooks/product-categories';
import { ProductCategoryInfo } from './product-category-info';

export function ProductCategoriesTable() {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useQueryState(
    'query',
    parseAsString.withDefault(''),
  );
  const [pagination, setPagination] = usePaginationSearchParams();
  const [sorting, setSorting] = useSortingQueryState();

  const [selectedCategoryId, setSelectedCategoryId] = useQueryState(
    'categoryId',
    {
      defaultValue: '',
    },
  );

  // State for showing inactive categories - show all by default
  const [showInactive, setShowInactive] = useState(true);

  const fetchParams = useMemo(
    () => ({
      sortBy: sorting.length > 0 ? sorting[0]?.id : undefined,
      direction:
        sorting.length > 0
          ? sorting[0]?.desc
            ? ('desc' as const)
            : ('asc' as const)
          : ('asc' as const),
      page: pagination.page,
      limit: pagination.pageSize,
      search: query ?? undefined,
    }),
    [query, pagination, sorting],
  );

  const { data, isPending, isError } = useGetProductCategories(fetchParams);

  const columns: ColumnDef<ProductCategory>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: () => <ColumnHeader label="Name" sortKey="name" />,
        cell: ({ row }) => (
          <div>
            <div className="text-sm font-medium">{row.original.name}</div>
            {row.original.label && (
              <div className="text-xs text-gray-500">{row.original.label}</div>
            )}
          </div>
        ),
      },
      {
        accessorKey: 'form',
        header: () => <ColumnHeader label="Form" sortKey="form" />,
        cell: ({ row }) => (
          <div className="text-sm">{row.original.form || '-'}</div>
        ),
      },
      {
        accessorKey: 'tags',
        header: () => <ColumnHeader label="Tags" sortKey="tags" />,
        cell: ({ row }) => (
          <div className="text-sm">
            {row.original.tags ? (
              <div className="flex flex-wrap gap-1">
                {row.original.tags.split(',').map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800"
                  >
                    {tag.trim()}
                  </span>
                ))}
              </div>
            ) : (
              '-'
            )}
          </div>
        ),
      },
      {
        accessorKey: '_count.Product',
        header: () => (
          <ColumnHeader label="Products" sortKey="_count.Product" />
        ),
        cell: ({ row }) => (
          <div className="text-sm">
            {row.original._count?.Product || 0} products
          </div>
        ),
      },
      {
        accessorKey: 'enabled',
        header: () => <ColumnHeader label="Status" sortKey="enabled" />,
        cell: ({ row }) => (
          <div
            className={cn(
              'inline-flex rounded-full px-2 py-1 text-xs font-medium',
              row.original.enabled
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800',
            )}
          >
            {row.original.enabled ? 'Active' : 'Inactive'}
          </div>
        ),
      },
      {
        accessorKey: 'order',
        header: () => <ColumnHeader label="Order" sortKey="order" />,
        cell: ({ row }) => <div className="text-sm">{row.original.order}</div>,
      },
    ],
    [],
  );

  // Filter categories based on showInactive
  const categories = useMemo(() => {
    const allCategories = data?.categories ?? [];

    if (!showInactive) {
      return allCategories.filter((category) => category.enabled);
    }

    return allCategories;
  }, [data, showInactive]);

  const table = useReactTable({
    data: categories,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
    manualPagination: true,
    pageCount: data?.pagination?.totalPages || 1,
  });

  const handleSearchSubmit = useCallback(
    (query: string | null) => {
      void setQuery(query);
      void setPagination({ page: 1 });
    },
    [setPagination, setQuery],
  );

  return (
    <div className="grid h-[calc(100vh-96px)] w-full grid-rows-[auto_1fr_auto]">
      <div className="w-full">
        <div className="mb-5 text-2xl font-medium text-denim">
          Product Categories
        </div>
        <SearchInput
          className="mb-5"
          ref={searchInputRef}
          onSearch={handleSearchSubmit}
          placeholder="Search categories"
          defaultValue={query}
        />
      </div>

      <div className="relative flex-1 overflow-auto">
        <Drawer
          direction="right"
          open={selectedCategoryId !== ''}
          onOpenChange={(value) => {
            if (!value) void setSelectedCategoryId('');
          }}
        >
          <div className="h-full w-full">
            {isPending ? (
              <div className="flex h-40 w-full items-center justify-center">
                <Loader size="lg" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          key={header.id}
                          className="font-bold text-dark"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (
                      <DrawerTrigger
                        key={row.id}
                        asChild
                        onClick={() => {
                          void setSelectedCategoryId(row.original.id);
                        }}
                      >
                        <TableRow
                          data-state={row.getIsSelected() && 'selected'}
                          className="h-[60px] cursor-pointer hover:bg-muted/50"
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id} className="p-2">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      </DrawerTrigger>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        {isError ? 'Error loading categories.' : 'No results.'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </div>
          {selectedCategoryId && (
            <DrawerPortal>
              <DrawerOverlay className="fixed inset-0 bg-dark/40" />
              <DrawerContent className="fixed bottom-0 right-0 top-0 z-10 flex w-[800px] !touch-none !select-text">
                <DrawerTitle className="hidden">
                  Category Information
                </DrawerTitle>
                <ProductCategoryInfo
                  categoryId={selectedCategoryId}
                  onClose={() => {
                    void setSelectedCategoryId('');
                  }}
                />
              </DrawerContent>
            </DrawerPortal>
          )}
        </Drawer>
      </div>

      <div className={cn('flex items-center justify-between py-4')}>
        <div className="flex gap-3">
          <Button onClick={() => setSelectedCategoryId('new')}>
            Add New Category
          </Button>
          <Button
            variant="electric"
            onClick={() => {
              setShowInactive(!showInactive);
              void setPagination({ page: 1 }); // Reset to first page when toggling
            }}
          >
            {showInactive
              ? 'Show Active Categories Only'
              : 'Show All Categories'}
          </Button>
        </div>
        {table.getRowModel().rows.length > 0 && (
          <Pagination
            currentPage={pagination.page}
            totalPages={table.getPageCount()}
            onPageChange={(page) => setPagination({ page })}
          />
        )}
      </div>
    </div>
  );
}
