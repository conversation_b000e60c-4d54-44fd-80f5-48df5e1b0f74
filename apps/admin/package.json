{"name": "@willow/admin", "version": "0.1.0", "private": true, "type": "module", "scripts": {"purge": "git clean -xdf .cache .turbo .next node_modules", "clean": "git clean -xdf .cache .turbo .next", "build": "pnpm with-env next build", "dev": "pnpm with-env bash -c 'next dev -p ${ADMIN_PORT:-3002}'", "start": "pnpm with-env next start -p ${ADMIN_PORT:-3002}", "format": "prettier --check . --ignore-path ../../.gitignore", "format:fix": "prettier --check --write . --ignore-path ../../.gitignore", "lint": "eslint", "lint:fix": "eslint --fix", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env -e ../../.env.local --"}, "dependencies": {"@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-query": "catalog:", "@willow/auth": "workspace:^", "@willow/db": "workspace:*", "@willow/ui": "workspace:*", "@willow/utils": "workspace:*", "axios": "^1.7.9", "big.js": "^6.2.2", "date-fns-tz": "^3.2.0", "jotai": "^2.10.3", "next": "^14.2.15", "nuqs": "^2.2.1", "react": "catalog:react18", "react-dom": "catalog:react18", "react-dropzone": "^14.3.8", "react-image-crop": "^11.0.7", "zod": "catalog:"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tanstack/react-query-devtools": "catalog:", "@types/big.js": "^6.2.2", "@types/node": "^22.5.0", "@types/react": "catalog:react18", "@types/react-dom": "catalog:react18", "@willow/eslint-config": "workspace:*", "@willow/prettier-config": "workspace:*", "@willow/tailwind-config": "workspace:*", "@willow/tsconfig": "workspace:*", "dotenv-cli": "^7.4.2", "eslint": "catalog:", "jiti": "^2.3.3", "prettier": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}, "prettier": "@willow/prettier-config"}