'use client';

import type { Key } from 'react';
import { useMemo } from 'react';
import Link from 'next/link';
import {
  calculateBMI,
  cn,
  convertInchesToCm,
  convertPoundsToKg,
} from '@/lib/utils';
import { intlFormat } from 'date-fns';

import { Button } from '@willow/ui/base/button';

import { FollowUpAnswers } from '~/components/followUp/FollowUpAnswers';
import {
  useCurrentPatient,
  useGetCompletedFollowUpsByPatientId,
  useGetPatientIntake,
} from '~/hooks/patient';

export default function VerificationPage({
  params: { patientId },
}: {
  params: { patientId: string };
}) {
  const patient = useCurrentPatient();
  const { data } = useGetPatientIntake(patientId);

  const showPrescribe = useMemo(
    () =>
      patient?.status
        ? [
            'pendingApprovalFromDoctor',
            'pendingPrescription',
            'activePrescription',
            'nonActivePrescription',
            'cancelled',
          ].includes(patient.status)
        : false,
    [patient?.status],
  );

  const formatObjectives = (answer: string) => {
    const answers = answer.split(/\s*,\s*/);
    const objectiveLabels: Record<string, string> = {
      loseFatWithoutLosingMuscle: 'Lose fat without losing muscle',
      decreaseFatigueIncreaseEnergy: 'Decrease fatigue and increase energy',
      supportHeartHealth: 'Support heart health',
      improveSkinLookAndFeel: 'Improve skin look and feel',
      dosingConcerns: 'Dosing concerns',
      noRefrigerationNeeded: 'No refrigeration needed',
      travelFriendly: 'Travel-friendly',
      none: 'None of these apply',
    };

    return answers.map((obj: string) => objectiveLabels[obj] || obj).join(', ');
  };

  if (!data || !patient) return <div>Loading patient data...</div>;

  return (
    <div className="min-h-fullh flex flex-col gap-10 px-12 py-10">
      <CompletedFollowUps patientId={patient.id} />

      <div className="font-['Neue Haas Grotesk Display Pro'] text-base font-medium leading-none text-zinc-500">
        Health Questions -{' '}
        {patient.createdAt &&
          intlFormat(patient.createdAt, {
            dateStyle: 'medium',
          })}
      </div>

      <div className="grid grid-cols-2 gap-10">
        {data?.record.map(
          (
            // @ts-expect-error any
            record,
            index: number,
          ) => (
            <div
              key={record as Key}
              className="flex flex-col justify-between gap-4 border-b border-zinc-200 py-1"
            >
              <div className="font-['PP Object Sans'] text-xs font-normal leading-none text-zinc-800">
                {index + 1} {'. '} {record.question}
              </div>
              <div className="font-['PP Object Sans'] text-sm font-normal text-zinc-800">
                {record.question === 'Do any of the following apply to you?'
                  ? formatObjectives(record.answer)
                  : record.answer.toString()}
              </div>
            </div>
          ),
        )}

        {patient.weight && patient.height && (
          <div className="flex flex-col justify-between gap-4 border-b border-zinc-200 py-1">
            <div className="text-xs font-normal text-zinc-800">BMI</div>
            <div className="text-sm font-normal text-zinc-800">
              {calculateBMI(
                convertPoundsToKg(patient.weight),
                convertInchesToCm(patient.height),
              )}
            </div>
          </div>
        )}
      </div>

      <div className="font-['Neue Haas Grotesk Display Pro'] text-base font-medium leading-none text-zinc-500">
        Requested Treatment
      </div>

      {patient.desiredTreatments && patient.desiredTreatments.length > 0 && (
        <ul className="flex flex-col gap-1">
          {patient.desiredTreatments.map((treatment) => (
            <li
              key={`${treatment.patientId}-${treatment.productId}`}
              className="flex items-center gap-2"
            >
              <span className="before:mr-2 before:content-['•']">
                {treatment.product?.name || 'Unknown product'}
              </span>
              <span
                className={cn(
                  'w-max rounded-full px-2 py-1 text-[13px] text-white',
                  `bg-months-month${treatment.vials ?? 1}`,
                )}
              >
                {treatment.vials ?? 1} Month
              </span>
            </li>
          ))}
        </ul>
      )}

      <div className="my-5 mt-auto flex gap-3 self-end pt-3">
        <Link href={`/patients/${patient.id}/messages`}>
          <Button size={'sm'} variant="denimOutline" className="px-10">
            Message
          </Button>
        </Link>

        {showPrescribe && (
          <Link href={`/patients/${patient.id}/prescribe`}>
            <Button size={'sm'} variant="denim" className="px-10 text-white">
              Prescribe
            </Button>
          </Link>
        )}
      </div>
    </div>
  );
}

const CompletedFollowUps = ({ patientId }: { patientId: string }) => {
  const { data: followUps } = useGetCompletedFollowUpsByPatientId(patientId);

  return followUps?.map((followUp) => (
    <FollowUpAnswers key={followUp.id} followUp={followUp} />
  ));
};
