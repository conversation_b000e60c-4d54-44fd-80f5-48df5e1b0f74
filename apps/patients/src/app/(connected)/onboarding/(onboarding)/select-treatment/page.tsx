'use client';

import type { ComponentType } from 'react';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useGetTreatments, useSelectTreatment } from '@/hooks/onboarding';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import GLP1InfoCard from '~/components/select-treatment/GLP1InfoCard';
import GLP190DaysInfoCard from '~/components/select-treatment/GLP190DaysInfoCard';
import {
  getProductDescription,
  TreatmentProductSelect,
  TreatmentProductSelectSmall,
} from '~/components/select-treatment/TreatmentCard';
import VialsCheckbox, {
  VialSubscriptionCheckbox,
} from '~/components/select-treatment/VialsCheckbox';
import { cn } from '~/lib/utils';

// Dynamically import the 90-day program card component
const Treatment90DayProgramCard = dynamic(
  () =>
    import('~/components/select-treatment/Treatment90DayProgramCard').then(
      (mod) => mod.Treatment90DayProgramCard,
    ),
  {
    loading: () => (
      <div className="h-[200px] w-full animate-pulse rounded-lg bg-white/10" />
    ),
    ssr: false,
  },
);

// Define props interface for treatment card components
interface TreatmentCardProps {
  id: string;
  name: string;
  image: string;
  price: number;
  isSelected: boolean;
  description?: string;
  type?: 'core' | 'additional';
  warning?: string | null;
  weightLoss?: number;
  onLearnMore?: () => void;
}

// Map for custom treatment card components based on product metadata
const customTreatmentCards: Record<
  string,
  ComponentType<TreatmentCardProps>
> = {
  '90-day': Treatment90DayProgramCard,
};

// Helper function to get the appropriate card component
const getTreatmentCardComponent = (
  product: Treatment,
): ComponentType<TreatmentCardProps> | null => {
  // Check for custom card based on metadata
  if (product.customCard) {
    const CustomCard = customTreatmentCards[product.customCard];
    if (CustomCard) {
      return CustomCard;
    }
  }

  // Return null to use default rendering
  return null;
};

export interface Treatment {
  id: string;
  price_id: string;
  name: string;
  product?: string;
  description: string;
  image: string;
  price: number;
  type: 'core' | 'additional';
  notice?: string;
  tags: string[];
  supplyLength: number;
  order: number;
  weightLossMultiplier: number;
  metadata: any;
  vials?: number;
  customCard?: string;
}

const schema = z
  .object({
    treatment: z.string().optional(),
    additionalTreatment: z.string().optional(),
    unsure: z.enum(['yes', 'no']).optional(),
    vials: z.number().optional(),
  })
  .refine(
    (data) => {
      if (data.unsure === 'yes') {
        return data.treatment === '' && data.additionalTreatment === '';
      } else {
        return data.treatment !== '';
      }
    },
    {
      path: ['unsure'],
      message:
        "Please select a treatment or indicate you're unsure and would like to speak with your Willow physician",
    },
  );

type FormType = z.infer<typeof schema>;

const TreatmentForm = () => {
  const [showSmallProductForm, setShowSmallProductForm] = useState(false);
  const [injectable, setInjectable] = useState(false);
  const [componentsReady, setComponentsReady] = useState(false);
  const analyticsData = useAnalyticsData();
  const { onboarding, nextOnboardingStep } = useOnboardingNavigation();
  const analytics = useAnalytics();
  const selected = onboarding?.context.products;
  const form = useForm<FormType>({
    resolver: zodResolver(schema),
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!onboarding) return;

    // The functionality to select multiple vials is temporarily disabled
    // const isMultiVialsEnabled = onboarding.context.questionnaire.usingGLP1 === 'yes';
    const isMultiVialsEnabled = false;
    const isInjectableType = onboarding.context.productType === 'injectable';

    setShowSmallProductForm(isMultiVialsEnabled && isInjectableType);

    setInjectable(isInjectableType);

    const treatment = selected?.find(
      (item) => item.id === getSelectedTreatmentId(),
    );

    // Rest of the code remains unchanged
    form.setValue('treatment', treatment?.id);
    form.setValue(
      'additionalTreatment',
      selected?.find((p) => p.type === 'additional')?.id ?? '',
    );
    form.setValue(
      'unsure',
      !selected ? undefined : selected.length === 0 ? 'yes' : 'no',
    );

    const vials = !isMultiVialsEnabled
      ? 1
      : (onboarding.context.products?.find((p) => p.type === 'core')?.vials ??
        1);
    form.setValue('vials', vials);
  }, [onboarding, form, selected]);

  const { data } = useGetTreatments(onboarding?.context.productType);

  const { mutateAsync, isPending: isFormPending } = useSelectTreatment();

  // Mark components as ready when data is loaded
  useEffect(() => {
    if (!data?.products || data.products.length === 0) return;

    // Small delay to ensure smooth transition
    const timer = setTimeout(() => {
      setComponentsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, [data]);

  const getSelectedTreatmentId = () =>
    selected?.find((p) => p.type !== 'additional')?.id ?? '';

  const getAnalyticsProducts = (
    selectedTreatments: (Treatment | undefined)[],
  ) =>
    selectedTreatments?.map((treatment) => ({
      product_id: treatment?.id,
      sku: treatment?.id,
      name: treatment?.name,
      price: treatment?.price,
      image_url: treatment?.image,
    }));

  const onSubmit = async ({
    treatment,
    additionalTreatment,
    vials,
  }: FormType) => {
    try {
      setIsLoading(true);
      const selectedTreatments = [];
      if (treatment && data) {
        selectedTreatments.push(
          data.products?.find(({ id }) => id === treatment),
        );
      }
      if (additionalTreatment) {
        selectedTreatments.push(
          additional.find(({ id }) => id === additionalTreatment),
        );
      }
      const response = await mutateAsync({
        products: selectedTreatments.map((item) => item?.id || ''),
        vials,
      });
      nextOnboardingStep(response.data);

      void analytics?.track('Treatment Chosen', {
        ProductPrices: selectedTreatments.map((item) => item?.price_id),
        ProductNames: selectedTreatments.map((item) => item?.name),
        products: getAnalyticsProducts(selectedTreatments),
        productIDs: selectedTreatments.map((item) => item?.id),
        patientChose: selectedTreatments.length != 0,
        ...analyticsData,
      });
    } catch (e: any) {
      setIsLoading(false);
      form.setError('unsure', { message: e.response.data?.message });
    }
  };

  const selectedTreatmentId =
    useWatch({
      control: form.control,
      name: 'treatment',
    }) || getSelectedTreatmentId();

  const selectedTreatment = data?.products.find(
    (product) => product.id === selectedTreatmentId,
  );
  const isSubscriptionProduct = selectedTreatment?.tags?.includes(
    'Subscription Treatment',
  );

  useEffect(() => {
    if (isSubscriptionProduct) form.setValue('additionalTreatment', undefined);
  }, [form, isSubscriptionProduct]);

  if (!data?.products || !onboarding) return null;

  const products = data.products.filter((p) => p.type === 'core');
  const additional = data.products.filter((p) => p.type === 'additional');

  const weight = onboarding.context.questionnaire.weight || 0;

  const getProductPrice = (treatmentId: string) =>
    products.find((product) => product.id === treatmentId)?.price ?? 0;

  return (
    <OnboardingTitle
      title="Our recommendation for you"
      subtitle="Don't worry! You will be able to adjust your selection later, and your doctor will confirm if it's right for you."
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col gap-8 md:gap-10"
        >
          <FormLoader isLoading={isFormPending || isLoading}>
            {data?.is90Days ? <GLP190DaysInfoCard /> : <GLP1InfoCard />}

            <div className="flex flex-col gap-4 md:gap-8">
              {showSmallProductForm && (
                <span className="text-2xl text-white">Choose Plan</span>
              )}

              <FormField
                control={form.control}
                name={'treatment'}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          form.setValue('vials', 1);
                          form.setValue('unsure', 'no');
                        }}
                        defaultValue={field.value}
                        className={cn('flex flex-col gap-5', {
                          'grid grid-cols-1 gap-2 md:grid-cols-3 md:gap-2':
                            showSmallProductForm,
                          'md:grid-cols-2': products.length === 2,
                        })}
                      >
                        {!componentsReady ? (
                          // Show loading skeletons while components are loading
                          <>
                            <div className="h-[200px] w-full animate-pulse rounded-lg bg-white/10" />
                            <div className="h-[200px] w-full animate-pulse rounded-lg bg-white/10" />
                          </>
                        ) : (
                          products.map((product) => {
                            // Check if we should use a custom card component
                            const CustomCardComponent =
                              getTreatmentCardComponent(product);

                            if (showSmallProductForm) {
                              return (
                                <TreatmentProductSelectSmall
                                  isSelected={field.value === product.id}
                                  key={product.id}
                                  id={product.id}
                                  name={product.name}
                                  learnMore={getProductDescription(
                                    product,
                                    onboarding.context.productType,
                                  )}
                                  price={product.price}
                                  image={product.image}
                                  weightLossMultiplier={
                                    product.weightLossMultiplier
                                  }
                                />
                              );
                            }

                            if (CustomCardComponent) {
                              return (
                                <CustomCardComponent
                                  key={product.id}
                                  id={product.id}
                                  name={product.name}
                                  image={product.image}
                                  price={product.price}
                                  isSelected={field.value === product.id}
                                  description={product.description}
                                  type={product.type}
                                  warning={product.notice}
                                  weightLoss={Math.floor(
                                    weight -
                                      weight * product.weightLossMultiplier,
                                  )}
                                  onLearnMore={() => {
                                    // Has built-in learn more dialog
                                  }}
                                />
                              );
                            }

                            return (
                              <TreatmentProductSelect
                                smallProduct={showSmallProductForm}
                                isSelected={field.value === product.id}
                                key={product.id}
                                component={
                                  <RadioGroupItem
                                    className="border-white"
                                    value={product.id}
                                    id={product.id}
                                    checked={field.value === product.id}
                                  />
                                }
                                componentId={product.id}
                                name={product.name}
                                description={product.description}
                                learnMore={getProductDescription(
                                  product,
                                  onboarding.context.productType,
                                )}
                                price={product.price}
                                type={product.type}
                                tags={product.tags}
                                warning={product.notice}
                                weightLoss={Math.floor(
                                  weight -
                                    weight * product.weightLossMultiplier,
                                )}
                                image={product.image}
                                weightLossMultiplier={
                                  product.weightLossMultiplier
                                }
                              />
                            );
                          })
                        )}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {showSmallProductForm && injectable && selectedTreatmentId && (
              <div className="flex flex-col gap-4 md:gap-8">
                <span className="text-2xl text-white">
                  Choose your delivery frequency
                </span>

                <FormField
                  control={form.control}
                  name="vials"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        {isSubscriptionProduct ? (
                          <VialSubscriptionCheckbox
                            field={field}
                            treatmentPrice={getProductPrice(
                              selectedTreatmentId,
                            )}
                          />
                        ) : (
                          <VialsCheckbox
                            field={field}
                            treatmentPrice={getProductPrice(
                              selectedTreatmentId,
                            )}
                          />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {additional.length > 0 && (
              <div className="mb-5 flex flex-col gap-4 md:gap-8">
                <div className="text-2xl text-white">Additional treatments</div>

                <div className="text-xl text-white">
                  Willow physician's recommend Ondansetron for treating nausea
                  and vomiting that some patients experience on GLP-1 medication
                  like Semaglutide and Tirzepatide.
                </div>

                <FormField
                  control={form.control}
                  name={'additionalTreatment'}
                  disabled={isSubscriptionProduct}
                  render={({ field }) => (
                    <FormItem
                      className={cn({
                        'opacity-60': isSubscriptionProduct,
                      })}
                    >
                      <FormControl>
                        <>
                          {additional.map((product) => (
                            <TreatmentProductSelect
                              smallProduct={false}
                              isSelected={field.value === product.id}
                              key={product.id}
                              component={
                                <Checkbox
                                  {...field}
                                  id={product.id}
                                  className="data-[state=checked]:bg-electric"
                                  value={product.id}
                                  checked={field.value === product.id}
                                  onCheckedChange={(checked) => {
                                    field.onChange(
                                      checked ? product.id : undefined,
                                    );
                                    form.setValue('unsure', 'no');
                                  }}
                                />
                              }
                              componentId={product.id}
                              name={product.name}
                              description={product.description}
                              learnMore={getProductDescription(product)}
                              price={product.price}
                              type={product.type}
                              tags={product.tags}
                              weightLoss={20}
                              image={product.image}
                            />
                          ))}
                        </>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <hr className="border-glass" />

            <div className="flex flex-col gap-4 md:gap-6">
              <span className="text-2xl text-white">
                Still unsure what you need?
              </span>
              <span className="text-xl text-white">
                Your Willow physician will be happy to explain the treatment
                options in more detail, answer any questions you may have, and
                modify your treatment choices if needed.
              </span>
            </div>

            <FormField
              control={form.control}
              name={'unsure'}
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center rounded-lg border border-glass backdrop-blur-xl">
                    <div className="inline-flex items-start justify-start gap-6 p-4">
                      <FormControl>
                        <div className="flex items-center">
                          <Checkbox
                            {...field}
                            id={'unsure'}
                            className="mr-4 data-[state=checked]:bg-electric"
                            value={field.value}
                            checked={field.value === 'yes'}
                            onCheckedChange={(checked) => {
                              field.onChange(checked ? 'yes' : 'no');
                              if (checked) {
                                form.setValue('treatment', '');
                                form.setValue('additionalTreatment', '');
                                form.setValue('vials', 0);
                              }
                            }}
                          />
                          <FormLabel
                            htmlFor="unsure"
                            className="text-sm text-white"
                          >
                            I'm unsure, and would like to speak with my Willow
                            physician.
                          </FormLabel>
                        </div>
                      </FormControl>
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              size="lg"
              variant="electric"
              className="mt-5 flex w-full max-w-none justify-between"
              type="submit"
              disabled={isFormPending}
            >
              <span>CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default TreatmentForm;
