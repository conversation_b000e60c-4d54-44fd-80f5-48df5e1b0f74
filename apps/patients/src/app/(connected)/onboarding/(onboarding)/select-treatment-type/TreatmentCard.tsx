import { useState } from 'react';
import Image from 'next/image';
import semaglutideTabletsMobileImg from '@/assets/png/semaglutide-tablets-mobile.png';
import semaglutideTabletsImg from '@/assets/png/semaglutide-tablets.png';
import { FormLabel } from '@/components/ui/form';
import { cn } from '@/lib/utils';
import classNames from 'classnames';

import styles from './treatment-card.module.css';

interface TreatmentCardProps {
  component: any;
  treatmentId: string;
  name: string;
  shortDescription: string;
  description?: string;
  image: string;
  price: number;
  tags: string[];
  isSelected: boolean;
}
const TreatmentCard = ({
  component,
  treatmentId,
  name,
  shortDescription,
  description,
  image,
  price,
  tags,
  isSelected,
}: TreatmentCardProps) => {
  const [showFullDescription, setShowFullDescription] = useState(false);
  const renderPrice = () => (
    <div className="font-medium">
      <span className="text-base">Starting at </span>
      <span>${price}</span>
      <span className="text-base">/mo</span>
    </div>
  );

  const renderTags = () =>
    tags.map((tag, index) => (
      <div
        key={index}
        className={classNames(
          'flex justify-start gap-2.5 rounded-2xl px-2 py-1',
          {
            ['bg-electric']: isSelected,
            ['bg-white']: !isSelected,
          },
        )}
      >
        <div className="text-xs font-normal italic text-denim">{tag}</div>
      </div>
    ));

  const isTabletTreatment = () => treatmentId == 'tablet';

  return (
    <div>
      <FormLabel
        htmlFor={treatmentId}
        className={classNames(
          'relative flex flex-col justify-center gap-2.5 rounded-xl border border-glass p-7 backdrop-blur-xl',
          {
            ['border-electric bg-white bg-opacity-10']: isSelected,
            [styles['treatment-card-bg'] ?? '']: isTabletTreatment(),
          },
        )}
      >
        <div className="flex gap-4">
          <div className={cn('mt-3 md:mt-8', tags.length && 'mt-6')}>
            {component}
          </div>
          <div className="flex w-full flex-col gap-4">
            <div className="flex w-full flex-row gap-4">
              <div
                className={classNames('flex items-center', {
                  ['md:hidden']: isTabletTreatment(),
                })}
              >
                <div
                  className={classNames(
                    'w-[70px] rounded-lg bg-gray-200 md:h-20 md:w-20',
                    {
                      ['bg-transparent']: isTabletTreatment(),
                    },
                  )}
                >
                  <Image
                    src={
                      isTabletTreatment() ? semaglutideTabletsMobileImg : image
                    }
                    width={100}
                    height={100}
                    alt="treatment"
                    className="h-full w-full rounded-lg object-cover"
                  />
                </div>
              </div>

              <div className="flex w-full flex-col justify-center gap-1">
                {tags.length > 0 && (
                  <div className="flex gap-2">{renderTags()}</div>
                )}

                <div className="flex flex-col justify-between gap-4">
                  <div className="flex w-full justify-between text-2xl font-medium leading-normal text-white">
                    <span>{name}</span>

                    <div className="hidden text-2xl font-normal text-white md:flex">
                      {renderPrice()}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-4 self-stretch">
              <span className="max-w-[500px] text-sm font-normal text-white">
                {showFullDescription ? description : shortDescription}
              </span>
              <span
                className="cursor-pointer text-sm font-normal text-white underline"
                onClick={() => setShowFullDescription(!showFullDescription)}
              >
                {showFullDescription ? 'Read less' : 'Read more'}
              </span>
            </div>

            <div className="text-2xl font-normal text-white md:hidden">
              {renderPrice()}
            </div>
          </div>
        </div>

        {isTabletTreatment() && (
          <Image
            src={semaglutideTabletsImg}
            width={100}
            height={100}
            alt="treatment"
            className="absolute bottom-0 right-7 hidden w-36 object-cover md:block"
          />
        )}
      </FormLabel>
    </div>
  );
};

export default TreatmentCard;
