'use client';

import type { ComponentType } from 'react';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useAnalytics } from '@/context/AnalyticsContext';
import {
  useGetTreatmentsType,
  useSelectTreatmentType,
} from '@/hooks/onboarding';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import TreatmentCard from './TreatmentCard';

const schema = z.object({
  categoryId: z.string().min(1, {
    message: 'Select a type of treatment',
  }),
});

type FormType = z.infer<typeof schema>;

// Define the props interface for treatment card components
interface TreatmentCardProps {
  component: any;
  treatmentId: string;
  name: string;
  shortDescription: string;
  description?: string;
  image: string;
  price: number;
  tags: string[];
  isSelected: boolean;
}

// Import all known custom card components
const TabletTreatmentCard = dynamic(() => import('./TabletTreatmentCard'), {
  loading: () => (
    <div className="h-[200px] w-full animate-pulse rounded-lg bg-white/10" />
  ),
  ssr: false,
});

// Define a static map of custom card components
const customCardComponents: Record<
  string,
  ComponentType<TreatmentCardProps>
> = {
  tablet: TabletTreatmentCard,
  // Add more custom card mappings here as needed
  // 'injectable': InjectableTreatmentCard,
  // '90-day': NinetyDayTreatmentCard,
};

// Helper function to get the appropriate card component
const getCardComponent = (
  customCard: string | null | undefined,
): ComponentType<TreatmentCardProps> => {
  if (!customCard) return TreatmentCard;

  // Check if we have a custom component for this card type
  const CustomComponent = customCardComponents[customCard.toLowerCase()];

  if (CustomComponent) {
    return CustomComponent;
  }

  // Log a warning if custom card is specified but not found
  console.warn(
    `Custom card component for "${customCard}" not found in mapping, using default TreatmentCard`,
  );

  return TreatmentCard;
};

const TreatmentForm = () => {
  const analyticsData = useAnalyticsData();
  const { onboarding, nextOnboardingStep } = useOnboardingNavigation();
  const analytics = useAnalytics();
  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      categoryId: '',
    },
  });
  const [isLoading, setIsLoading] = useState(false);
  const [componentsReady, setComponentsReady] = useState(false);

  useEffect(() => {
    if (!onboarding) return;
    form.setValue('categoryId', onboarding?.context.productType || '');
  }, [onboarding, form]);

  const { data: treatmentTypes } = useGetTreatmentsType();
  const { mutateAsync, isPending: isFormPending } = useSelectTreatmentType();

  // Mark components as ready when treatment types are loaded
  // Since we're using static imports, components are already available
  useEffect(() => {
    if (!treatmentTypes || treatmentTypes.length === 0) return;

    // Small delay to ensure smooth transition
    const timer = setTimeout(() => {
      setComponentsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, [treatmentTypes]);

  const onSubmit = async ({ categoryId }: FormType) => {
    try {
      if (!categoryId) {
        form.setError('categoryId', {
          message: 'Please select a treatment type',
        });
        return;
      }

      setIsLoading(true);

      const response = await mutateAsync({ categoryId });
      nextOnboardingStep(response.data);

      void analytics?.track('Treatment Type Chosen', {
        categoryId: categoryId,
        ...analyticsData,
      });
    } catch (e: any) {
      setIsLoading(false);
      form.setError('categoryId', { message: e.response.data?.message });
    }
  };

  if (!treatmentTypes || !onboarding) return null;

  return (
    <OnboardingTitle
      title="Our doctors recommend these treatment options"
      subtitle="Dosage will vary based on physician's recommendation. You'll have the opportunity to review the treatment plan with your Willow physician."
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col gap-6"
        >
          <p className="text-base font-normal text-white">
            Here are the 2 ways to receive GLP-1 weight loss therapy:
          </p>

          <FormLoader isLoading={isFormPending || isLoading}>
            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <RadioGroup
                      value={field.value}
                      onValueChange={(value) => field.onChange(value)}
                      defaultValue={field.value}
                      className="flex flex-col gap-5"
                    >
                      {!componentsReady ? (
                        // Show loading skeletons while components are loading
                        <>
                          <div className="h-[200px] w-full animate-pulse rounded-lg bg-white/10" />
                          <div className="h-[200px] w-full animate-pulse rounded-lg bg-white/10" />
                        </>
                      ) : (
                        treatmentTypes.map((treatmentType) => {
                          const CardComponent = getCardComponent(
                            treatmentType.customCard,
                          );

                          return (
                            <CardComponent
                              isSelected={field.value === treatmentType.id}
                              key={treatmentType.id}
                              component={
                                <RadioGroupItem
                                  className="border-white"
                                  value={treatmentType.id}
                                  id={treatmentType.id}
                                  checked={field.value === treatmentType.id}
                                />
                              }
                              treatmentId={treatmentType.id}
                              name={treatmentType.name}
                              shortDescription={
                                treatmentType.shortDescription || ''
                              }
                              description={treatmentType.description || ''}
                              price={treatmentType.basePrice}
                              image={treatmentType.image || ''}
                              tags={treatmentType.tags || []}
                            />
                          );
                        })
                      )}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              size="lg"
              variant="electric"
              className="mt-10 flex w-full max-w-none justify-between"
              type="submit"
              disabled={isFormPending}
            >
              <span>CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default TreatmentForm;
