import type {
  BillingAddress,
  OnboardingData,
  ShippingAddress,
  ShippingInfo,
  TreatmentType,
} from '@/data/types';
import { interceptorInjected } from '@/store/store';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useAtomValue } from 'jotai/index';

import { apiClient } from '@willow/utils/api/client';

import type { DiscountInfo } from './onboarding-discount';
import type { Treatment } from '~/app/(connected)/onboarding/(onboarding)/select-treatment/page';
import { getUTMsForBackend } from '~/lib/utm-capture';

export const useOnboardingNext = <T>() => {
  return useMutation<{ data: OnboardingData }, unknown, T>({
    mutationFn: (data) => apiClient.post(`/onboarding/next`, data),
  });
};

export const useOnboardingQuestionnaire = <E = unknown, T = unknown>() => {
  return useMutation<
    { data: OnboardingData },
    unknown,
    { event?: E; value?: T }
  >({
    mutationFn: ({ event, value }) => {
      return apiClient.post(
        `/onboarding/questionnaire`,
        {
          event: event ?? 'next',
          value,
        },
        { headers: getUTMsForBackend() as any },
      );
    },
  });
};

export const useOnboardingBack = () => {
  return useMutation<{ data: OnboardingData }>({
    mutationFn: () => apiClient.post(`/onboarding/back`),
  });
};

export const useGetTreatments = (productType?: string) => {
  const injected = useAtomValue(interceptorInjected);
  return useQuery({
    queryKey: ['recommended-treatment', productType],
    queryFn: () =>
      apiClient.get(`/onboarding/recommended-treatment/${productType}`).then(
        (res) =>
          res.data as {
            usesGLP1: boolean;
            products: Treatment[];
            is90Days: boolean;
            [key: string]: any;
          },
      ),
    enabled: injected && !!productType,
  });
};

export const useGetTreatmentsType = () => {
  const injected = useAtomValue(interceptorInjected);
  return useQuery({
    queryKey: ['treatment-types'],
    queryFn: () =>
      apiClient
        .get(`/onboarding/treatment-types`)
        .then((res) => res.data as TreatmentType[]),
    enabled: injected,
  });
};

export const useSelectTreatment = () => {
  return useMutation<
    { data: OnboardingData },
    unknown,
    { products: string[]; vials?: number }
  >({
    mutationFn: (data) => apiClient.post(`/onboarding/desired-treatment`, data),
  });
};

export const useSelectTreatmentType = () => {
  return useMutation<{ data: OnboardingData }, unknown, { categoryId: string }>(
    {
      mutationFn: (data) =>
        apiClient.post(`/onboarding/desired-treatment-type`, data),
    },
  );
};

//ONBOARDING: AQUI SE VERIFICA LA DIRRECICON
export const useSetShipping = () => {
  return useMutation<{ data: OnboardingData }, unknown, ShippingInfo>({
    mutationFn: (data) => apiClient.post(`/onboarding/shipping-info`, data),
  });
};

export const useGetStripeClientSecret = (type: 'onboarding' | 'dashboard') => {
  const injected = useAtomValue(interceptorInjected);
  const path = type === 'onboarding' ? 'onboarding' : 'patient';
  return useQuery({
    queryKey: ['stripe-client-secret', type],
    queryFn: () =>
      apiClient
        .post(`/${path}/setup-payment-intent`)
        .then((res) => res.data.clientSecret as string),
    enabled: injected,
  });
};

export interface CheckoutInfo {
  stripe: {
    clientSecret: string;
  };
  discount: DiscountInfo | null;
  totalPriceBeforeDiscount: string;
  discountPrice: string;
  totalDiscountedPrice: string;
}

export const useOboardingCheckoutInfo = () => {
  const injected = useAtomValue(interceptorInjected);
  return useQuery({
    queryKey: ['onboarding', 'checkout-info'],
    queryFn: () =>
      apiClient
        .post(`/onboarding/setup-payment-intent`)
        .then((res) => res.data as CheckoutInfo),
    enabled: injected,
  });
};

export const useUpdateBilling = (type: 'onboarding' | 'dashboard') => {
  const path =
    type === 'onboarding'
      ? '/onboarding/update-billing-address'
      : '/patient/billingAddress';
  return useMutation<{ data: OnboardingData }, unknown, ShippingInfo>({
    mutationFn: (data) =>
      apiClient.post(path, data).catch((e) => {
        if (e.response?.data) throw e.response.data;
        throw e;
      }),
  });
};

export const useGetBilling = () => {
  return useQuery({
    queryKey: ['billing-address'],
    queryFn: () =>
      apiClient.get('/patient/billingAddress').then((res) => {
        return res.data.address as BillingAddress;
      }),
  });
};

export const useGetShipping = () => {
  return useQuery({
    queryKey: ['shipping-address'],
    queryFn: () =>
      apiClient.get('/patient/shippingAddress').then((res) => {
        return res.data.shippingAddresses as ShippingAddress[];
      }),
  });
};
