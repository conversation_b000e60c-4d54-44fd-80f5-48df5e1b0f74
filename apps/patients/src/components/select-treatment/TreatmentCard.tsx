import { useState } from 'react';
import Image from 'next/image';
import { FormLabel } from '@/components/ui/form';
import { RadioGroupItem } from '@/components/ui/radio-group';
import { TreatmentDialog } from '@/components/ui/TreatmentDialog';
import classNames from 'classnames';

import { cn } from '~/lib/utils';
import { MobileTreatmentCard } from './MobileTreatmentCard';
import TreatmentCardContent from './TreatmentCardContent';

export const getProductDescription = (data: any, productType?: string) => {
  const descriptions: any = {
    semaglutide: (productType?: string) => (
      <div className="flex flex-col gap-4 text-left text-sm font-normal text-white">
        <p>
          Semaglutide is a drug that falls under glucagon-like peptide-1
          receptor agonists, or GLP-1 RAs, and works like the GLP-1 hormone that
          our gut releases when we eat.
        </p>
        <p>
          Its main job is to help the body make more insulin, which lowers blood
          sugar. That's why doctors have been prescribing it to treat Type 2
          diabetes for over 15 years.
        </p>
        <p>
          Also, when there's more GLP-1, it affects brain areas that control how
          hungry we feel and when we feel full. Used with a healthy diet and
          regular exercise, semaglutide can lead to significant weight loss and
          lower the risk of getting cancer, diabetes, and heart disease in
          people who are overweight or obese.
        </p>
        {productType === 'tablet' && (
          <p>
            Semaglutide can also be taken as oral dissolvable sublingual
            tablets.These tablets are placed under the tongue once a day, where
            they dissolve and deliver the medication directly into the
            bloodstream through the thin tissue in the mouth. This daily,
            needle-free option offers a convenient and discreet way to take
            GLP-1 medication, combining proven results with ease of use.
          </p>
        )}
      </div>
    ),
    tirzepatide: () => (
      <div className="flex flex-col gap-4 text-left text-sm font-normal text-white">
        <p>
          Tirzepatide and Semaglutide are injectable drugs used for managing
          type 2 diabetes and obesity, acting as GLP-1 receptor agonists to
          simulate the GLP-1 hormone, regulating blood sugar and appetite.
        </p>
        <p>
          The main difference is that Tirzepatide targets both GLP-1 and GIP
          (glucose-dependent insulinotropic polypeptide) receptors, with GIP
          also playing a role in blood sugar management, whereas Semaglutide
          focuses on the GLP-1 receptor alone. Both medications are administered
          as a weekly subcutaneous injection.
        </p>
        <p>
          In effectiveness, Tirzepatide outperforms Semaglutide in lowering
          blood sugar and aiding weight loss. Studies show Tirzepatide users
          lost an average of 22.5% body weight over 72 weeks, compared to 15.3%
          with Semaglutide.
        </p>
        <p>
          Tirzepatide is also more effective at improving cardiovascular risk
          factors, such as blood pressure, cholesterol, and triglycerides.
        </p>
      </div>
    ),
    ondansetron: () => (
      <div className="flex flex-col gap-4 text-left text-sm font-normal text-white">
        <p>
          Ondansetron is an antiemetic (or anti-nausea) medication. It's used to
          prevent nausea and vomiting. It works by stopping a chemical called
          serotonin from binding to receptors in the brain that cause the
          feeling of nausea and vomiting.
        </p>
        <p>
          Ondansetron is taken by mouth, typically every 8 to 12 hours. It comes
          in different formulations, including orally disintegrating tablet
          (ODT) and oral solution. It can also be given as injection by a
          healthcare professional (HCP) in a hospital setting. Side effects of
          this medication include headache, tiredness, and constipation or
          diarrhea.
        </p>
      </div>
    ),
  };

  return descriptions[data.product]?.(productType);
};

export const TreatmentProductSelect = ({
  component,
  componentId,
  name,
  description,
  learnMore,
  image,
  price,
  type,
  tags,
  warning,
  weightLoss,
  isSelected,
  smallProduct,
}: {
  component: any;
  componentId: string;
  name: string;
  description: string;
  learnMore?: string;
  image: string;
  price: number;
  type: 'core' | 'additional';
  tags: string[];
  warning?: string | null;
  weightLoss: number;
  isSelected: boolean;
  smallProduct: boolean;
  weightLossMultiplier?: number;
}) => {
  const [showInfoModal, setShowInfoModal] = useState(false);
  const onNext = () => {
    setShowInfoModal(false);
  };

  const renderPrice = () => (
    <div>
      <span>${price}</span>
      {type === 'core' && <span className="text-sm">/mo</span>}
    </div>
  );

  return (
    <div>
      <FormLabel
        htmlFor={componentId}
        className={classNames(
          'flex flex-col items-start justify-center gap-2.5 rounded-lg border border-glass p-7 backdrop-blur-xl',
          {
            ['border-electric bg-white bg-opacity-10']: isSelected,
          },
        )}
      >
        {/* Mobile Layout */}
        <MobileTreatmentCard
          id={componentId}
          name={name}
          image={image}
          price={renderPrice()}
          isSelected={isSelected}
          description={description}
          type={type}
          warning={warning}
          weightLoss={weightLoss}
          tags={tags}
          onClickLearnMore={() => setShowInfoModal(true)}
          learnMore={learnMore}
          radioButton={component}
        />

        {/* Desktop Layout */}
        <div className="hidden items-start gap-4 md:flex">
          <div
            className={cn('mt-3 md:mt-12', {
              '!mt-0': smallProduct,
            })}
          >
            {component}
          </div>
          <div className="flex flex-row items-start justify-start gap-4 self-stretch">
            <div className="flex items-center">
              <div
                className={cn('w-12 rounded-lg bg-gray-200 md:w-28', {
                  'w-20': smallProduct,
                })}
              >
                <Image
                  src={image}
                  width={120}
                  height={120}
                  alt="treatment"
                  className="h-full w-full rounded-lg object-cover"
                />
              </div>
            </div>

            <div className="flex shrink grow basis-0 flex-col items-start justify-start gap-4">
              {tags.length > 0 && (
                <div className="flex gap-2">
                  {tags.map((tag, index) => (
                    <div
                      key={index}
                      className={classNames(
                        'flex items-start justify-start gap-2.5 rounded-2xl px-2 py-1',
                        {
                          ['bg-electric']: isSelected,
                          ['bg-white']: !isSelected,
                        },
                      )}
                    >
                      <div className="text-xs font-normal text-denim">
                        {tag}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              <TreatmentCardContent
                name={name}
                description={description}
                learnMore={learnMore}
                onClickLearnMore={() => setShowInfoModal(true)}
                price={renderPrice()}
                type={type}
                warning={warning}
                weightLoss={weightLoss}
              />
            </div>
          </div>
        </div>
      </FormLabel>

      {showInfoModal && learnMore && (
        <TreatmentDialog
          title={name}
          image={image}
          description={learnMore}
          confirmBtnText="CLOSE"
          confirmBtnClick={() => setShowInfoModal(false)}
          closeBtnClick={() => {
            onNext();
            setShowInfoModal(false);
          }}
        />
      )}
    </div>
  );
};

const getAvgWeightLoss = (weightLossMultiplier: number) =>
  100 - weightLossMultiplier * 100;

export const TreatmentProductSelectSmall = ({
  id,
  name,
  learnMore,
  image,
  price,
  isSelected,
  weightLossMultiplier,
}: {
  id: string;
  name: string;
  learnMore?: string;
  image: string;
  price: number;
  isSelected: boolean;
  weightLossMultiplier?: number;
}) => {
  const [showInfoModal, setShowInfoModal] = useState(false);
  const onNext = () => {
    setShowInfoModal(false);
  };

  return (
    <div>
      <FormLabel
        htmlFor={id}
        className={classNames(
          'flex h-full flex-col gap-2.5 rounded-lg border border-glass p-7 backdrop-blur-xl',
          {
            ['border-electric bg-white bg-opacity-10']: isSelected,
          },
        )}
      >
        <div>
          <RadioGroupItem
            className="mb-4 h-6 w-6 border-white"
            value={id}
            id={id}
            checked={isSelected}
          />
          <div>
            <Image
              src={image}
              width={96}
              height={96}
              alt="treatment"
              className="h-24 w-24 rounded-lg object-cover"
            />
          </div>

          <div className="flex flex-col gap-2 py-3">
            <span className="text-2xl text-white">{name}</span>
            <span className="text-md text-white">Starting At ${price}/mo</span>
            {weightLossMultiplier && (
              <div className="self-stretch text-sm font-normal text-white">
                {getAvgWeightLoss(weightLossMultiplier)}% avg. weight loss
              </div>
            )}
            {learnMore && (
              <span
                className="cursor-pointer text-sm font-normal text-white underline"
                onClick={() => setShowInfoModal(true)}
              >
                Learn more about {name}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-4 self-stretch"></div>
        </div>
      </FormLabel>

      {showInfoModal && learnMore && (
        <TreatmentDialog
          title={name}
          image={image}
          description={learnMore}
          confirmBtnText="CLOSE"
          confirmBtnClick={() => setShowInfoModal(false)}
          closeBtnClick={() => {
            onNext();
            setShowInfoModal(false);
          }}
        />
      )}
    </div>
  );
};
