import type { ReactNode } from 'react';
import * as React from 'react';
import Image from 'next/image';

import { Button } from './button';
import { Dialog } from './dialog';

interface Props {
  title: string;
  image?: string;
  description: string | ReactNode;
  confirmBtnText: string;
  confirmBtnClick: () => void;
  closeBtnText?: string;
  closeBtnClick: () => void;
}

const TreatmentDialog = ({
  title,
  image,
  description,
  confirmBtnText,
  confirmBtnClick,
  closeBtnText,
  closeBtnClick,
}: Props) => {
  return (
    <Dialog
      variant="dark"
      className="mb-0 rounded-none p-4 md:mb-10 md:rounded-lg md:p-10"
    >
      <div className="relative flex max-w-[490px] flex-col gap-4 text-left">
        <div className="mb-4 flex flex-col items-center justify-center gap-6">
          <div className="font-['Neue Haas Grotesk Display Pro'] self-stretch pt-4 text-left text-4xl font-medium leading-10 text-white md:pt-0 md:text-center md:text-5xl">
            {title}
          </div>
          {image && (
            <div className="w-full rounded-lg bg-gray-200">
              <Image
                src={image}
                width={200}
                height={200}
                alt="treatment"
                className="h-full w-full rounded-lg object-cover"
              />
            </div>
          )}
          <div className="font-['PP Object Sans'] self-stretch text-xl font-normal text-white">
            {description}
          </div>
        </div>

        <Button
          onClick={confirmBtnClick}
          size="lg"
          variant="outlinePrimary"
          className="w-full border-white px-12 text-lg font-semibold text-white md:w-auto"
        >
          <span> {confirmBtnText}</span>
        </Button>

        {closeBtnText && (
          <Button
            onClick={closeBtnClick}
            size="lg"
            variant="outline"
            className="w-full border-2 border-slate-500 px-12 text-lg font-semibold text-denim md:w-auto"
          >
            <span>{closeBtnText}</span>
          </Button>
        )}
      </div>
    </Dialog>
  );
};

TreatmentDialog.displayName = 'TreatmentDialog';

export { TreatmentDialog };
