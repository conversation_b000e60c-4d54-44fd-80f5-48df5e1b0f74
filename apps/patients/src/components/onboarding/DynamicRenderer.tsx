import type { OnboardingData } from '@/data/types';
import React from 'react';
import AdditionalInformation from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/AdditionalInformation';
import Age from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/Age';
import DesiredWeight from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/DesiredWeight';
import DoctorVisits from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/DoctorVisits';
import Eligible from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/Eligible';
import Gender from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/Gender';
import HaveAllergies from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/HaveAllergies';
import HaveDiabetes from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/HaveDiabetes';
import Height from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/Height';
import IsPregnant from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/IsPregnant';
import MedicalConditions from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/MedicalConditions';
import Medications from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/Medications';
import Objectives from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/Objectives';
import QualifyingConditions from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/QualifyingConditions';
import RecommendSeeDoctor from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/RecommendSeeDoctor';
import RejectedIsPregnant from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/RejectedIsPregnant';
import RejectedOverAge from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/RejectedOverAge';
import RejectedPriorConditions from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/RejectedPriorConditions';
import RejectedUnderAge from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/RejectedUnderAge';
import SelectAllergies from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/SelectAllergies';
import UsingGLP1 from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/UsingGLP1';
import Weight from '@/app/(connected)/onboarding/(onboarding)/questionnaire/pages/Weight';

// Define your component map outside of your component function
const componentMap: Record<string, any> = {
  age: Age,
  gender: Gender,
  rejectedUnderAge: RejectedUnderAge,
  rejectedOverAge: RejectedOverAge,
  isPregnant: IsPregnant,
  rejectedIsPregnant: RejectedIsPregnant,
  usingGLP1: UsingGLP1,
  haveDiabetes: HaveDiabetes,
  rejectedPriorConditions: RejectedPriorConditions,
  eligible: Eligible,
  doctorVisits: DoctorVisits,
  recommendSeeDoctor: RecommendSeeDoctor,
  qualifyingConditions: QualifyingConditions,
  height: Height,
  weight: Weight,
  desiredWeight: DesiredWeight,
  haveAllergies: HaveAllergies,
  selectAllergies: SelectAllergies,
  medications: Medications,
  medicalConditions: MedicalConditions,
  objectives: Objectives,
  additionalInformation: AdditionalInformation,
};

function DynamicRenderer(props: {
  componentName: string;
  data: OnboardingData;
  callback: (response: OnboardingData) => void;
}) {
  const { data, callback, componentName } = props;
  // Determine the component to render based on the componentName prop
  const ComponentToRender = componentMap[props.componentName];

  // Render the component if it exists in the map, otherwise render a fallback message
  return (
    <div>
      {ComponentToRender ? (
        <ComponentToRender data={data} callback={callback} />
      ) : (
        <div>Component {componentName} not found</div>
      )}
    </div>
  );
}

export default DynamicRenderer;
