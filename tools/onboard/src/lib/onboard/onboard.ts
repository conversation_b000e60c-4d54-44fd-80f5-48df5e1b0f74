import { faker } from '@faker-js/faker';

import { prisma } from '@willow/db';

import type { OnboardConfig } from './types';
import { getApiUrl } from '../constants';
import { OnboardingClient } from './api';
import { generateQuestionnaireData, generateUserData } from './generators';
import { uploadPatientPhotos } from './photos';
import { setupStripeCustomer } from './stripe';
import { envSchema } from './types';

const env = envSchema.parse(process.env);
const POLLING_INTERVAL = 1000; // 1 second
const MAX_POLLING_TIME = 60000; // 1 minute

const validLocations = [
  { state: 'AZ', city: 'Phoenix', zip: '85004' },
  { state: 'AZ', city: 'Tucson', zip: '85701' },
  { state: 'CA', city: 'San Francisco', zip: '94105' },
  { state: 'CA', city: 'Los Angeles', zip: '90012' },
  { state: 'CO', city: 'Denver', zip: '80202' },
  { state: 'CO', city: 'Boulder', zip: '80302' },
  { state: 'CT', city: 'Hartford', zip: '06103' },
  { state: 'FL', city: 'Miami', zip: '33131' },
  { state: 'FL', city: 'Orlando', zip: '32801' },
  { state: 'GA', city: 'Atlanta', zip: '30303' },
  { state: 'ID', city: 'Boise', zip: '83702' },
  { state: 'IL', city: 'Chicago', zip: '60601' },
  { state: 'IN', city: 'Indianapolis', zip: '46204' },
  { state: 'MD', city: 'Baltimore', zip: '21202' },
  { state: 'MN', city: 'Minneapolis', zip: '55401' },
  { state: 'NV', city: 'Las Vegas', zip: '89101' },
  { state: 'NY', city: 'New York', zip: '10007' },
  { state: 'TX', city: 'Austin', zip: '78701' },
  { state: 'VA', city: 'Richmond', zip: '23219' },
  { state: 'WA', city: 'Seattle', zip: '98101' },
];

async function pollForOnboardingCompletion(patientId: string): Promise<void> {
  const startTime = Date.now();

  while (Date.now() - startTime < MAX_POLLING_TIME) {
    const patient = await prisma.patient.findUnique({
      where: { id: patientId },
      select: { status: true },
    });

    if (patient?.status !== 'onboardingPending') {
      return;
    }

    await new Promise((resolve) => setTimeout(resolve, POLLING_INTERVAL));
  }

  throw new Error('Onboarding completion timeout');
}

async function getRandomState(code?: string) {
  if (code) {
    const [state] = await prisma.$queryRaw<{ id: string; code: string }[]>`
        SELECT * FROM "State"
        WHERE enabled = true
        AND code = ${code}
        LIMIT 1
      `;
    if (!state) throw new Error('State not found or not enabled');
    return state;
  }

  const [state] = await prisma.$queryRaw<{ id: string; code: string }[]>`
        SELECT * FROM "State"
        WHERE enabled = true
        ORDER BY RANDOM()
        LIMIT 1
      `;

  if (!state?.code) throw new Error('State not found');
  return state;
}

export async function createPatient(config: OnboardConfig) {
  // Initialize API client
  const baseUrl = getApiUrl(env.ENVIRONMENT);
  const api = new OnboardingClient(baseUrl);

  // Get random state
  const state = await getRandomState(config.state);

  // Generate user data
  const userData = generateUserData(config.name, state.code);
  const questionnaire = generateQuestionnaireData(
    userData.birthDate,
    userData.gender,
  );

  try {
    // Signup
    const { patientId } = await api.signup({
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      password: userData.password,
      phone: userData.phone,
      state: state.code,
      getPromotionsSMS: false,
    });

    // Complete questionnaire
    await api.submitBirthDate(questionnaire.birthDate);
    await api.submitGender(questionnaire.gender);
    if (questionnaire.gender === 'female') {
      await api.submitPregnancyStatus(false);
    }
    await api.submitGLP1Status(questionnaire.usingGLP1 === 'yes');
    await api.submitDiabetesStatus(questionnaire.haveDiabetes === 'yes');
    await api.submitQuestionnaireStep('next');
    const doctorVisits = questionnaire.doctorVisits === 'yes';
    await api.submitDoctorVisits(doctorVisits);
    if (!doctorVisits) {
      await api.submitQuestionnaireStep('next');
    }
    await api.submitQualifyingConditions(questionnaire.qualifyingConditions);
    await api.submitHeight(questionnaire.height);
    await api.submitWeight(questionnaire.weight);
    await api.submitDesiredWeight(questionnaire.desiredWeight);
    await api.submitObjectives(questionnaire.objectives);
    await api.submitAllergiesStatus(questionnaire.hasAllergies === 'yes');

    if (questionnaire.hasAllergies === 'yes') {
      await api.submitAllergies(questionnaire.allergies);
    }

    await api.submitMedications(questionnaire.medications);

    await api.submitMedicalConditions(questionnaire.medicalConditions);

    await api.submitAdditionalInfo(questionnaire.additionalInformation);

    // Return early if questionnaire only
    if (config.questionnaireOnly) {
      return {
        email: userData.email,
        state: state.code,
      };
    }

    const treatmentTypes = await api.getTreatmentTypes();

    let desiredTreatment: string[] = [];

    if (treatmentTypes.length > 0) {
      const selectedType = faker.helpers.arrayElement(treatmentTypes);
      await api.setDesiredTreatmentType(selectedType.id);

      const recommendedTreatments = await api.getRecommendedTreatment(
        selectedType.id,
      );

      if (!config.noProducts) {
        const additionalTreatment = recommendedTreatments.products.find(
          (t) => t.type === 'additional',
        )?.id;
        const coreTreatments = recommendedTreatments.products.filter(
          (t) => t.type === 'core',
        );

        if (coreTreatments.length > 0) {
          const coreTreatment = faker.helpers.arrayElement(coreTreatments).id;

          if (additionalTreatment && coreTreatment) {
            desiredTreatment = [additionalTreatment, coreTreatment];
          } else if (coreTreatment) {
            desiredTreatment = [coreTreatment];
          }
        }
      }
    }

    const vials = 1;

    await api.setDesiredTreatment(desiredTreatment, vials);

    // Step 3: Move to photos step
    await api.next();

    if (!config.noPhotos) {
      await uploadPatientPhotos(patientId, env, questionnaire.gender);
    }

    await api.confirmPhotoUpload('id-photo', config.noPhotos);
    await api.confirmPhotoUpload('face-photo', config.noPhotos);

    // Move to visit completion
    await api.next();

    // Move to summary
    await api.next();

    // Submit shipping info
    // Filter locations to match the selected state
    const stateLocations = validLocations.filter(
      (location) => location.state === state.code,
    );

    // If no locations match the selected state, fall back to any location
    const locationPool =
      stateLocations.length > 0 ? stateLocations : validLocations;

    await api.submitShippingInfo({
      address1: userData.address1,
      address2: userData.address2,
      ...faker.helpers.arrayElement(locationPool),
      force: true,
    });

    // Setup Stripe customer and wait for onboarding completion if not incomplete
    if (!config.incomplete) {
      await api.setupPaymentIntent();

      const { stripeCustomerId: id } = await prisma.patient.findFirstOrThrow({
        where: { id: patientId },
      });
      if (!id) throw new Error('Stripe customer ID not found');

      await setupStripeCustomer({ id, ...userData }, config.noFunds);

      await pollForOnboardingCompletion(patientId);
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to create patient: ${errorMessage}`);
  }

  return {
    email: userData.email,
    state: state.code,
  };
}
