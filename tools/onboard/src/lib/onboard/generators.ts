import { faker } from '@faker-js/faker';
import dayjs from 'dayjs';
import capitalize from 'lodash/capitalize';

/**
 * Sanitizes a string for use in email addresses
 * Removes special characters, converts spaces to dots, and ensures lowercase
 * @param str String to sanitize
 * @returns Sanitized string safe for email use
 */
function sanitizeForEmail(str: string): string {
  return str
    .normalize('NFD') // Decompose accented characters
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[^\w\s-]/g, '') // Remove special chars
    .replace(/\s+/g, '.') // Replace spaces with dots
    .replace(/-+/g, '.') // Replace hyphens with dots
    .replace(/\.{2,}/g, '.') // Replace multiple dots with single dot
    .toLowerCase(); // Convert to lowercase
}

export function generateUserData(name: string, state: string) {
  const gender = faker.helpers.weightedArrayElement([
    { weight: 7, value: 'female' },
    { weight: 3, value: 'male' },
  ]);
  const firstName = faker.person.firstName(gender);
  const lastName = faker.person.lastName(gender);

  // Sanitize first and last name for email generation
  const sanitizedFirstName = sanitizeForEmail(firstName);
  const sanitizedLastName = sanitizeForEmail(lastName);

  const areaCode = faker.helpers.arrayElement([
    201, 202, 203, 205, 206, 207, 208, 209, 210, 212, 213, 214, 215, 216, 217,
    218, 219, 220, 223, 224, 225, 227, 228, 229, 231, 234, 235, 239, 240, 248,
    251, 252, 253, 254, 256, 260, 262, 267, 269, 270, 272, 274, 276, 279, 281,
    283, 301, 302, 303, 304, 305, 307, 308, 309, 310, 312, 313, 314, 315, 316,
    317, 318, 319, 320, 321, 323, 324, 325, 326, 327, 329, 330, 331, 332, 334,
    336, 337, 339, 341, 346, 347, 350, 351, 352, 353, 360, 361, 363, 364, 369,
    380, 385, 386, 401, 402, 404, 405, 406, 407, 408, 409, 410, 412, 413, 414,
    415, 417, 419, 423, 424, 425, 430, 432, 434, 435, 436, 440, 442, 443, 445,
    447, 448, 458, 463, 464, 469, 470, 472, 475, 478, 479, 480, 484, 501, 502,
    503, 504, 505, 507, 508, 509, 510, 512, 513, 515, 516, 517, 518, 520, 530,
    531, 534, 539, 540, 541, 551, 557, 559, 561, 562, 563, 564, 567, 570, 571,
    572, 573, 574, 575, 580, 582, 585, 586, 601, 602, 603, 605, 606, 607, 608,
    609, 610, 612, 614, 615, 616, 617, 618, 619, 620, 623, 624, 626, 628, 629,
    630, 631, 636, 640, 641, 645, 646, 650, 651, 656, 657, 659, 660, 661, 662,
    667, 669, 678, 680, 681, 682, 686, 689, 701, 702, 703, 704, 706, 707, 708,
    712, 713, 714, 715, 716, 717, 718, 719, 720, 724, 725, 726, 727, 728, 730,
    731, 732, 734, 737, 740, 743, 747, 754, 757, 760, 762, 763, 765, 769, 770,
    771, 772, 773, 774, 775, 779, 781, 785, 786, 801, 802, 803, 804, 805, 806,
    808, 810, 812, 813, 814, 815, 816, 817, 818, 820, 826, 828, 830, 831, 832,
    835, 838, 839, 840, 843, 845, 847, 848, 850, 854, 856, 857, 858, 859, 860,
    861, 862, 863, 864, 865, 870, 872, 878, 901, 903, 904, 906, 907, 908, 909,
    910, 912, 913, 914, 915, 916, 917, 918, 919, 920, 925, 928, 929, 930, 931,
    934, 936, 937, 938, 940, 941, 943, 945, 947, 948, 949, 951, 952, 954, 956,
    959, 970, 971, 972, 973, 975, 978, 979, 980, 983, 984, 985, 986, 989,
  ]);
  const prefix = faker.number.int({ min: 200, max: 999 }).toString();
  const lineNumber = faker.number.int({ min: 1000, max: 9999 }).toString();
  const phone = `(${areaCode}) ${prefix}-${lineNumber}`;
  const birthDate = dayjs(
    faker.date.between({
      from: dayjs().subtract(60, 'years').toDate(),
      to: dayjs().subtract(20, 'years').toDate(),
    }),
  ).format('YYYY-MM-DD');

  const address = {
    address1: faker.location.streetAddress(),
    address2:
      faker.helpers.maybe(() => faker.location.secondaryAddress(), {
        probability: 0.3,
      }) ?? '',
    city: faker.location.city(),
    state,
    zip: faker.location.zipCode('#####'),
  };

  // Use sanitized names for the email but keep original names for display
  return {
    firstName: `${firstName} (${capitalize(name)})`,
    lastName,
    gender,
    email: `${name}+${sanitizedFirstName}.${sanitizedLastName}@startwillow.com`,
    password: 'somePassword1234$',
    phone,
    birthDate,
    ...address,
  };
}

export function generateQuestionnaireData(
  birthDate: string,
  gender: 'male' | 'female',
) {
  const weight = faker.number.int({ min: 140, max: 300 });
  const qualifyingConditionsList = [
    'highBloodPressure',
    'highLipids',
    'highCholesterol',
    'obstructiveSleepApnea',
    'cardiovascularDisease',
  ];
  const allergiesList = [
    'peanuts',
    'shellfish',
    'eggs',
    'dairy',
    'gluten',
    'soy',
    'fish',
    'treeNuts',
  ];
  const medicalConditionsList = [
    'asthma',
    'arthritis',
    'cancer',
    'depression',
    'diabetes',
    'epilepsy',
    'heartDisease',
    'highBloodPressure',
    'highCholesterol',
    'migraines',
    'osteoporosis',
    'sleepApnea',
    'thyroidDisease',
  ];

  const objectivesList = [
    'loseFatWithoutLosingMuscle',
    'decreaseFatigueIncreaseEnergy',
    'supportHeartHealth',
    'improveSkinLookAndFeel',
    'dosingConcerns',
    'noRefrigerationNeeded',
    'travelFriendly',
  ];

  // 60% of the time it should be none, 40% random objectives
  const hasObjectives = faker.datatype.boolean({ probability: 0.4 });
  const objectives = hasObjectives
    ? faker.helpers.arrayElements(objectivesList, { min: 1, max: 3 })
    : [];

  return {
    birthDate,
    gender,
    isPregnant: gender === 'male' ? undefined : 'no',
    usingGLP1: faker.helpers.arrayElement(['yes', 'no']),
    haveDiabetes: 'no',
    doctorVisits: faker.helpers.arrayElement(['yes', 'no']),
    qualifyingConditions: faker.helpers.arrayElements(
      qualifyingConditionsList,
      { min: 1, max: 3 },
    ),
    height: faker.number.int({ min: 58, max: 77 }),
    weight,
    desiredWeight: faker.number.int({
      min: 120,
      max: Math.min(160, weight - 1),
    }),
    objectives,
    hasAllergies: faker.helpers.arrayElement(['yes', 'no']),
    allergies:
      faker.helpers.maybe(
        () => faker.helpers.arrayElements(allergiesList, { min: 1, max: 3 }),
        { probability: 0.8 },
      ) ?? [],
    medications:
      faker.helpers.maybe(
        () =>
          Array.from(
            { length: faker.number.int({ min: 1, max: 4 }) },
            () => faker.science.chemicalElement().name,
          ),
        { probability: 0.8 },
      ) ?? [],
    medicalConditions:
      faker.helpers.maybe(
        () =>
          faker.helpers.arrayElements(medicalConditionsList, {
            min: 1,
            max: 3,
          }),
        { probability: 0.8 },
      ) ?? [],
    additionalInformation:
      faker.helpers.maybe(() => faker.lorem.paragraph({ min: 3, max: 6 }), {
        probability: 0.8,
      }) ?? '',
  };
}
